function xy(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const o=Object.getOwnPropertyDescriptor(r,i);o&&Object.defineProperty(e,i,o.get?o:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function lp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var up={exports:{}},ds={},cp={exports:{}},B={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ri=Symbol.for("react.element"),wy=Symbol.for("react.portal"),Sy=Symbol.for("react.fragment"),Cy=Symbol.for("react.strict_mode"),ky=Symbol.for("react.profiler"),Py=Symbol.for("react.provider"),by=Symbol.for("react.context"),Ey=Symbol.for("react.forward_ref"),Ty=Symbol.for("react.suspense"),Ny=Symbol.for("react.memo"),jy=Symbol.for("react.lazy"),Nc=Symbol.iterator;function Ry(e){return e===null||typeof e!="object"?null:(e=Nc&&e[Nc]||e["@@iterator"],typeof e=="function"?e:null)}var dp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},fp=Object.assign,pp={};function Er(e,t,n){this.props=e,this.context=t,this.refs=pp,this.updater=n||dp}Er.prototype.isReactComponent={};Er.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Er.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function hp(){}hp.prototype=Er.prototype;function Jl(e,t,n){this.props=e,this.context=t,this.refs=pp,this.updater=n||dp}var eu=Jl.prototype=new hp;eu.constructor=Jl;fp(eu,Er.prototype);eu.isPureReactComponent=!0;var jc=Array.isArray,mp=Object.prototype.hasOwnProperty,tu={current:null},gp={key:!0,ref:!0,__self:!0,__source:!0};function vp(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)mp.call(t,r)&&!gp.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:Ri,type:e,key:o,ref:s,props:i,_owner:tu.current}}function My(e,t){return{$$typeof:Ri,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function nu(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ri}function Ay(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Rc=/\/+/g;function Ws(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Ay(""+e.key):t.toString(36)}function ho(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Ri:case wy:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Ws(s,0):r,jc(i)?(n="",e!=null&&(n=e.replace(Rc,"$&/")+"/"),ho(i,t,n,"",function(u){return u})):i!=null&&(nu(i)&&(i=My(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Rc,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",jc(e))for(var a=0;a<e.length;a++){o=e[a];var l=r+Ws(o,a);s+=ho(o,t,n,l,i)}else if(l=Ry(e),typeof l=="function")for(e=l.call(e),a=0;!(o=e.next()).done;)o=o.value,l=r+Ws(o,a++),s+=ho(o,t,n,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function $i(e,t,n){if(e==null)return e;var r=[],i=0;return ho(e,r,"","",function(o){return t.call(n,o,i++)}),r}function _y(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Le={current:null},mo={transition:null},Ly={ReactCurrentDispatcher:Le,ReactCurrentBatchConfig:mo,ReactCurrentOwner:tu};function yp(){throw Error("act(...) is not supported in production builds of React.")}B.Children={map:$i,forEach:function(e,t,n){$i(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return $i(e,function(){t++}),t},toArray:function(e){return $i(e,function(t){return t})||[]},only:function(e){if(!nu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};B.Component=Er;B.Fragment=Sy;B.Profiler=ky;B.PureComponent=Jl;B.StrictMode=Cy;B.Suspense=Ty;B.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ly;B.act=yp;B.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=fp({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=tu.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)mp.call(t,l)&&!gp.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Ri,type:e.type,key:i,ref:o,props:r,_owner:s}};B.createContext=function(e){return e={$$typeof:by,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Py,_context:e},e.Consumer=e};B.createElement=vp;B.createFactory=function(e){var t=vp.bind(null,e);return t.type=e,t};B.createRef=function(){return{current:null}};B.forwardRef=function(e){return{$$typeof:Ey,render:e}};B.isValidElement=nu;B.lazy=function(e){return{$$typeof:jy,_payload:{_status:-1,_result:e},_init:_y}};B.memo=function(e,t){return{$$typeof:Ny,type:e,compare:t===void 0?null:t}};B.startTransition=function(e){var t=mo.transition;mo.transition={};try{e()}finally{mo.transition=t}};B.unstable_act=yp;B.useCallback=function(e,t){return Le.current.useCallback(e,t)};B.useContext=function(e){return Le.current.useContext(e)};B.useDebugValue=function(){};B.useDeferredValue=function(e){return Le.current.useDeferredValue(e)};B.useEffect=function(e,t){return Le.current.useEffect(e,t)};B.useId=function(){return Le.current.useId()};B.useImperativeHandle=function(e,t,n){return Le.current.useImperativeHandle(e,t,n)};B.useInsertionEffect=function(e,t){return Le.current.useInsertionEffect(e,t)};B.useLayoutEffect=function(e,t){return Le.current.useLayoutEffect(e,t)};B.useMemo=function(e,t){return Le.current.useMemo(e,t)};B.useReducer=function(e,t,n){return Le.current.useReducer(e,t,n)};B.useRef=function(e){return Le.current.useRef(e)};B.useState=function(e){return Le.current.useState(e)};B.useSyncExternalStore=function(e,t,n){return Le.current.useSyncExternalStore(e,t,n)};B.useTransition=function(){return Le.current.useTransition()};B.version="18.3.1";cp.exports=B;var y=cp.exports;const re=lp(y),xp=xy({__proto__:null,default:re},[y]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dy=y,Vy=Symbol.for("react.element"),Iy=Symbol.for("react.fragment"),Oy=Object.prototype.hasOwnProperty,Fy=Dy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,zy={key:!0,ref:!0,__self:!0,__source:!0};function wp(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Oy.call(t,r)&&!zy.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Vy,type:e,key:o,ref:s,props:i,_owner:Fy.current}}ds.Fragment=Iy;ds.jsx=wp;ds.jsxs=wp;up.exports=ds;var h=up.exports,_a={},Sp={exports:{}},Xe={},Cp={exports:{}},kp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,_){var F=N.length;N.push(_);e:for(;0<F;){var V=F-1>>>1,$=N[V];if(0<i($,_))N[V]=_,N[F]=$,F=V;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var _=N[0],F=N.pop();if(F!==_){N[0]=F;e:for(var V=0,$=N.length,Y=$>>>1;V<Y;){var Ue=2*(V+1)-1,Wn=N[Ue],$e=Ue+1,vn=N[$e];if(0>i(Wn,F))$e<$&&0>i(vn,Wn)?(N[V]=vn,N[$e]=F,V=$e):(N[V]=Wn,N[Ue]=F,V=Ue);else if($e<$&&0>i(vn,F))N[V]=vn,N[$e]=F,V=$e;else break e}}return _}function i(N,_){var F=N.sortIndex-_.sortIndex;return F!==0?F:N.id-_.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],d=1,c=null,f=3,g=!1,w=!1,x=!1,C=typeof setTimeout=="function"?setTimeout:null,v=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(N){for(var _=n(u);_!==null;){if(_.callback===null)r(u);else if(_.startTime<=N)r(u),_.sortIndex=_.expirationTime,t(l,_);else break;_=n(u)}}function S(N){if(x=!1,m(N),!w)if(n(l)!==null)w=!0,z(k);else{var _=n(u);_!==null&&Q(S,_.startTime-N)}}function k(N,_){w=!1,x&&(x=!1,v(E),E=-1),g=!0;var F=f;try{for(m(_),c=n(l);c!==null&&(!(c.expirationTime>_)||N&&!O());){var V=c.callback;if(typeof V=="function"){c.callback=null,f=c.priorityLevel;var $=V(c.expirationTime<=_);_=e.unstable_now(),typeof $=="function"?c.callback=$:c===n(l)&&r(l),m(_)}else r(l);c=n(l)}if(c!==null)var Y=!0;else{var Ue=n(u);Ue!==null&&Q(S,Ue.startTime-_),Y=!1}return Y}finally{c=null,f=F,g=!1}}var b=!1,P=null,E=-1,R=5,j=-1;function O(){return!(e.unstable_now()-j<R)}function D(){if(P!==null){var N=e.unstable_now();j=N;var _=!0;try{_=P(!0,N)}finally{_?H():(b=!1,P=null)}}else b=!1}var H;if(typeof p=="function")H=function(){p(D)};else if(typeof MessageChannel<"u"){var A=new MessageChannel,G=A.port2;A.port1.onmessage=D,H=function(){G.postMessage(null)}}else H=function(){C(D,0)};function z(N){P=N,b||(b=!0,H())}function Q(N,_){E=C(function(){N(e.unstable_now())},_)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){w||g||(w=!0,z(k))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(N){switch(f){case 1:case 2:case 3:var _=3;break;default:_=f}var F=f;f=_;try{return N()}finally{f=F}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,_){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var F=f;f=N;try{return _()}finally{f=F}},e.unstable_scheduleCallback=function(N,_,F){var V=e.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?V+F:V):F=V,N){case 1:var $=-1;break;case 2:$=250;break;case 5:$=**********;break;case 4:$=1e4;break;default:$=5e3}return $=F+$,N={id:d++,callback:_,priorityLevel:N,startTime:F,expirationTime:$,sortIndex:-1},F>V?(N.sortIndex=F,t(u,N),n(l)===null&&N===n(u)&&(x?(v(E),E=-1):x=!0,Q(S,F-V))):(N.sortIndex=$,t(l,N),w||g||(w=!0,z(k))),N},e.unstable_shouldYield=O,e.unstable_wrapCallback=function(N){var _=f;return function(){var F=f;f=_;try{return N.apply(this,arguments)}finally{f=F}}}})(kp);Cp.exports=kp;var By=Cp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uy=y,Ge=By;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Pp=new Set,ai={};function On(e,t){mr(e,t),mr(e+"Capture",t)}function mr(e,t){for(ai[e]=t,e=0;e<t.length;e++)Pp.add(t[e])}var Rt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),La=Object.prototype.hasOwnProperty,$y=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Mc={},Ac={};function Wy(e){return La.call(Ac,e)?!0:La.call(Mc,e)?!1:$y.test(e)?Ac[e]=!0:(Mc[e]=!0,!1)}function Hy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ky(e,t,n,r){if(t===null||typeof t>"u"||Hy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function De(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var ke={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ke[e]=new De(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ke[t]=new De(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ke[e]=new De(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ke[e]=new De(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ke[e]=new De(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ke[e]=new De(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ke[e]=new De(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ke[e]=new De(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ke[e]=new De(e,5,!1,e.toLowerCase(),null,!1,!1)});var ru=/[\-:]([a-z])/g;function iu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ru,iu);ke[t]=new De(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ru,iu);ke[t]=new De(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ru,iu);ke[t]=new De(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ke[e]=new De(e,1,!1,e.toLowerCase(),null,!1,!1)});ke.xlinkHref=new De("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ke[e]=new De(e,1,!1,e.toLowerCase(),null,!0,!0)});function ou(e,t,n,r){var i=ke.hasOwnProperty(t)?ke[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ky(t,n,i,r)&&(n=null),r||i===null?Wy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Dt=Uy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Wi=Symbol.for("react.element"),Gn=Symbol.for("react.portal"),Qn=Symbol.for("react.fragment"),su=Symbol.for("react.strict_mode"),Da=Symbol.for("react.profiler"),bp=Symbol.for("react.provider"),Ep=Symbol.for("react.context"),au=Symbol.for("react.forward_ref"),Va=Symbol.for("react.suspense"),Ia=Symbol.for("react.suspense_list"),lu=Symbol.for("react.memo"),zt=Symbol.for("react.lazy"),Tp=Symbol.for("react.offscreen"),_c=Symbol.iterator;function Mr(e){return e===null||typeof e!="object"?null:(e=_c&&e[_c]||e["@@iterator"],typeof e=="function"?e:null)}var se=Object.assign,Hs;function $r(e){if(Hs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Hs=t&&t[1]||""}return`
`+Hs+e}var Ks=!1;function Gs(e,t){if(!e||Ks)return"";Ks=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,a=o.length-1;1<=s&&0<=a&&i[s]!==o[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==o[a]){if(s!==1||a!==1)do if(s--,a--,0>a||i[s]!==o[a]){var l=`
`+i[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{Ks=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?$r(e):""}function Gy(e){switch(e.tag){case 5:return $r(e.type);case 16:return $r("Lazy");case 13:return $r("Suspense");case 19:return $r("SuspenseList");case 0:case 2:case 15:return e=Gs(e.type,!1),e;case 11:return e=Gs(e.type.render,!1),e;case 1:return e=Gs(e.type,!0),e;default:return""}}function Oa(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Qn:return"Fragment";case Gn:return"Portal";case Da:return"Profiler";case su:return"StrictMode";case Va:return"Suspense";case Ia:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ep:return(e.displayName||"Context")+".Consumer";case bp:return(e._context.displayName||"Context")+".Provider";case au:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case lu:return t=e.displayName||null,t!==null?t:Oa(e.type)||"Memo";case zt:t=e._payload,e=e._init;try{return Oa(e(t))}catch{}}return null}function Qy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Oa(t);case 8:return t===su?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function an(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Np(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Yy(e){var t=Np(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Hi(e){e._valueTracker||(e._valueTracker=Yy(e))}function jp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Np(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function No(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Fa(e,t){var n=t.checked;return se({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Lc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=an(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Rp(e,t){t=t.checked,t!=null&&ou(e,"checked",t,!1)}function za(e,t){Rp(e,t);var n=an(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ba(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ba(e,t.type,an(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Dc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ba(e,t,n){(t!=="number"||No(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Wr=Array.isArray;function ur(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+an(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Ua(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return se({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Vc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(Wr(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:an(n)}}function Mp(e,t){var n=an(t.value),r=an(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ic(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ap(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function $a(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ap(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ki,_p=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ki=Ki||document.createElement("div"),Ki.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ki.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function li(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Qr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Xy=["Webkit","ms","Moz","O"];Object.keys(Qr).forEach(function(e){Xy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Qr[t]=Qr[e]})});function Lp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Qr.hasOwnProperty(e)&&Qr[e]?(""+t).trim():t+"px"}function Dp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Lp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var qy=se({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Wa(e,t){if(t){if(qy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function Ha(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ka=null;function uu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ga=null,cr=null,dr=null;function Oc(e){if(e=_i(e)){if(typeof Ga!="function")throw Error(T(280));var t=e.stateNode;t&&(t=gs(t),Ga(e.stateNode,e.type,t))}}function Vp(e){cr?dr?dr.push(e):dr=[e]:cr=e}function Ip(){if(cr){var e=cr,t=dr;if(dr=cr=null,Oc(e),t)for(e=0;e<t.length;e++)Oc(t[e])}}function Op(e,t){return e(t)}function Fp(){}var Qs=!1;function zp(e,t,n){if(Qs)return e(t,n);Qs=!0;try{return Op(e,t,n)}finally{Qs=!1,(cr!==null||dr!==null)&&(Fp(),Ip())}}function ui(e,t){var n=e.stateNode;if(n===null)return null;var r=gs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var Qa=!1;if(Rt)try{var Ar={};Object.defineProperty(Ar,"passive",{get:function(){Qa=!0}}),window.addEventListener("test",Ar,Ar),window.removeEventListener("test",Ar,Ar)}catch{Qa=!1}function Zy(e,t,n,r,i,o,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var Yr=!1,jo=null,Ro=!1,Ya=null,Jy={onError:function(e){Yr=!0,jo=e}};function e0(e,t,n,r,i,o,s,a,l){Yr=!1,jo=null,Zy.apply(Jy,arguments)}function t0(e,t,n,r,i,o,s,a,l){if(e0.apply(this,arguments),Yr){if(Yr){var u=jo;Yr=!1,jo=null}else throw Error(T(198));Ro||(Ro=!0,Ya=u)}}function Fn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Bp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Fc(e){if(Fn(e)!==e)throw Error(T(188))}function n0(e){var t=e.alternate;if(!t){if(t=Fn(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return Fc(i),e;if(o===r)return Fc(i),t;o=o.sibling}throw Error(T(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s){for(a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s)throw Error(T(189))}}if(n.alternate!==r)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function Up(e){return e=n0(e),e!==null?$p(e):null}function $p(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=$p(e);if(t!==null)return t;e=e.sibling}return null}var Wp=Ge.unstable_scheduleCallback,zc=Ge.unstable_cancelCallback,r0=Ge.unstable_shouldYield,i0=Ge.unstable_requestPaint,de=Ge.unstable_now,o0=Ge.unstable_getCurrentPriorityLevel,cu=Ge.unstable_ImmediatePriority,Hp=Ge.unstable_UserBlockingPriority,Mo=Ge.unstable_NormalPriority,s0=Ge.unstable_LowPriority,Kp=Ge.unstable_IdlePriority,fs=null,xt=null;function a0(e){if(xt&&typeof xt.onCommitFiberRoot=="function")try{xt.onCommitFiberRoot(fs,e,void 0,(e.current.flags&128)===128)}catch{}}var pt=Math.clz32?Math.clz32:c0,l0=Math.log,u0=Math.LN2;function c0(e){return e>>>=0,e===0?32:31-(l0(e)/u0|0)|0}var Gi=64,Qi=4194304;function Hr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ao(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~i;a!==0?r=Hr(a):(o&=s,o!==0&&(r=Hr(o)))}else s=n&~i,s!==0?r=Hr(s):o!==0&&(r=Hr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-pt(t),i=1<<n,r|=e[n],t&=~i;return r}function d0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function f0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-pt(o),a=1<<s,l=i[s];l===-1?(!(a&n)||a&r)&&(i[s]=d0(a,t)):l<=t&&(e.expiredLanes|=a),o&=~a}}function Xa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Gp(){var e=Gi;return Gi<<=1,!(Gi&4194240)&&(Gi=64),e}function Ys(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Mi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-pt(t),e[t]=n}function p0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-pt(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function du(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var K=0;function Qp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Yp,fu,Xp,qp,Zp,qa=!1,Yi=[],Qt=null,Yt=null,Xt=null,ci=new Map,di=new Map,$t=[],h0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Bc(e,t){switch(e){case"focusin":case"focusout":Qt=null;break;case"dragenter":case"dragleave":Yt=null;break;case"mouseover":case"mouseout":Xt=null;break;case"pointerover":case"pointerout":ci.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":di.delete(t.pointerId)}}function _r(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=_i(t),t!==null&&fu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function m0(e,t,n,r,i){switch(t){case"focusin":return Qt=_r(Qt,e,t,n,r,i),!0;case"dragenter":return Yt=_r(Yt,e,t,n,r,i),!0;case"mouseover":return Xt=_r(Xt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return ci.set(o,_r(ci.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,di.set(o,_r(di.get(o)||null,e,t,n,r,i)),!0}return!1}function Jp(e){var t=bn(e.target);if(t!==null){var n=Fn(t);if(n!==null){if(t=n.tag,t===13){if(t=Bp(n),t!==null){e.blockedOn=t,Zp(e.priority,function(){Xp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function go(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Za(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ka=r,n.target.dispatchEvent(r),Ka=null}else return t=_i(n),t!==null&&fu(t),e.blockedOn=n,!1;t.shift()}return!0}function Uc(e,t,n){go(e)&&n.delete(t)}function g0(){qa=!1,Qt!==null&&go(Qt)&&(Qt=null),Yt!==null&&go(Yt)&&(Yt=null),Xt!==null&&go(Xt)&&(Xt=null),ci.forEach(Uc),di.forEach(Uc)}function Lr(e,t){e.blockedOn===t&&(e.blockedOn=null,qa||(qa=!0,Ge.unstable_scheduleCallback(Ge.unstable_NormalPriority,g0)))}function fi(e){function t(i){return Lr(i,e)}if(0<Yi.length){Lr(Yi[0],e);for(var n=1;n<Yi.length;n++){var r=Yi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Qt!==null&&Lr(Qt,e),Yt!==null&&Lr(Yt,e),Xt!==null&&Lr(Xt,e),ci.forEach(t),di.forEach(t),n=0;n<$t.length;n++)r=$t[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<$t.length&&(n=$t[0],n.blockedOn===null);)Jp(n),n.blockedOn===null&&$t.shift()}var fr=Dt.ReactCurrentBatchConfig,_o=!0;function v0(e,t,n,r){var i=K,o=fr.transition;fr.transition=null;try{K=1,pu(e,t,n,r)}finally{K=i,fr.transition=o}}function y0(e,t,n,r){var i=K,o=fr.transition;fr.transition=null;try{K=4,pu(e,t,n,r)}finally{K=i,fr.transition=o}}function pu(e,t,n,r){if(_o){var i=Za(e,t,n,r);if(i===null)oa(e,t,r,Lo,n),Bc(e,r);else if(m0(i,e,t,n,r))r.stopPropagation();else if(Bc(e,r),t&4&&-1<h0.indexOf(e)){for(;i!==null;){var o=_i(i);if(o!==null&&Yp(o),o=Za(e,t,n,r),o===null&&oa(e,t,r,Lo,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else oa(e,t,r,null,n)}}var Lo=null;function Za(e,t,n,r){if(Lo=null,e=uu(r),e=bn(e),e!==null)if(t=Fn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Bp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Lo=e,null}function eh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(o0()){case cu:return 1;case Hp:return 4;case Mo:case s0:return 16;case Kp:return 536870912;default:return 16}default:return 16}}var Ht=null,hu=null,vo=null;function th(){if(vo)return vo;var e,t=hu,n=t.length,r,i="value"in Ht?Ht.value:Ht.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return vo=i.slice(e,1<r?1-r:void 0)}function yo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Xi(){return!0}function $c(){return!1}function qe(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Xi:$c,this.isPropagationStopped=$c,this}return se(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Xi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Xi)},persist:function(){},isPersistent:Xi}),t}var Tr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},mu=qe(Tr),Ai=se({},Tr,{view:0,detail:0}),x0=qe(Ai),Xs,qs,Dr,ps=se({},Ai,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Dr&&(Dr&&e.type==="mousemove"?(Xs=e.screenX-Dr.screenX,qs=e.screenY-Dr.screenY):qs=Xs=0,Dr=e),Xs)},movementY:function(e){return"movementY"in e?e.movementY:qs}}),Wc=qe(ps),w0=se({},ps,{dataTransfer:0}),S0=qe(w0),C0=se({},Ai,{relatedTarget:0}),Zs=qe(C0),k0=se({},Tr,{animationName:0,elapsedTime:0,pseudoElement:0}),P0=qe(k0),b0=se({},Tr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),E0=qe(b0),T0=se({},Tr,{data:0}),Hc=qe(T0),N0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},j0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},R0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function M0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=R0[e])?!!t[e]:!1}function gu(){return M0}var A0=se({},Ai,{key:function(e){if(e.key){var t=N0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=yo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?j0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gu,charCode:function(e){return e.type==="keypress"?yo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?yo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),_0=qe(A0),L0=se({},ps,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Kc=qe(L0),D0=se({},Ai,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gu}),V0=qe(D0),I0=se({},Tr,{propertyName:0,elapsedTime:0,pseudoElement:0}),O0=qe(I0),F0=se({},ps,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),z0=qe(F0),B0=[9,13,27,32],vu=Rt&&"CompositionEvent"in window,Xr=null;Rt&&"documentMode"in document&&(Xr=document.documentMode);var U0=Rt&&"TextEvent"in window&&!Xr,nh=Rt&&(!vu||Xr&&8<Xr&&11>=Xr),Gc=String.fromCharCode(32),Qc=!1;function rh(e,t){switch(e){case"keyup":return B0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ih(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Yn=!1;function $0(e,t){switch(e){case"compositionend":return ih(t);case"keypress":return t.which!==32?null:(Qc=!0,Gc);case"textInput":return e=t.data,e===Gc&&Qc?null:e;default:return null}}function W0(e,t){if(Yn)return e==="compositionend"||!vu&&rh(e,t)?(e=th(),vo=hu=Ht=null,Yn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return nh&&t.locale!=="ko"?null:t.data;default:return null}}var H0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Yc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!H0[e.type]:t==="textarea"}function oh(e,t,n,r){Vp(r),t=Do(t,"onChange"),0<t.length&&(n=new mu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qr=null,pi=null;function K0(e){gh(e,0)}function hs(e){var t=Zn(e);if(jp(t))return e}function G0(e,t){if(e==="change")return t}var sh=!1;if(Rt){var Js;if(Rt){var ea="oninput"in document;if(!ea){var Xc=document.createElement("div");Xc.setAttribute("oninput","return;"),ea=typeof Xc.oninput=="function"}Js=ea}else Js=!1;sh=Js&&(!document.documentMode||9<document.documentMode)}function qc(){qr&&(qr.detachEvent("onpropertychange",ah),pi=qr=null)}function ah(e){if(e.propertyName==="value"&&hs(pi)){var t=[];oh(t,pi,e,uu(e)),zp(K0,t)}}function Q0(e,t,n){e==="focusin"?(qc(),qr=t,pi=n,qr.attachEvent("onpropertychange",ah)):e==="focusout"&&qc()}function Y0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return hs(pi)}function X0(e,t){if(e==="click")return hs(t)}function q0(e,t){if(e==="input"||e==="change")return hs(t)}function Z0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var mt=typeof Object.is=="function"?Object.is:Z0;function hi(e,t){if(mt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!La.call(t,i)||!mt(e[i],t[i]))return!1}return!0}function Zc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jc(e,t){var n=Zc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Zc(n)}}function lh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?lh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function uh(){for(var e=window,t=No();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=No(e.document)}return t}function yu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function J0(e){var t=uh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&lh(n.ownerDocument.documentElement,n)){if(r!==null&&yu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Jc(n,o);var s=Jc(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var ex=Rt&&"documentMode"in document&&11>=document.documentMode,Xn=null,Ja=null,Zr=null,el=!1;function ed(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;el||Xn==null||Xn!==No(r)||(r=Xn,"selectionStart"in r&&yu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Zr&&hi(Zr,r)||(Zr=r,r=Do(Ja,"onSelect"),0<r.length&&(t=new mu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Xn)))}function qi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var qn={animationend:qi("Animation","AnimationEnd"),animationiteration:qi("Animation","AnimationIteration"),animationstart:qi("Animation","AnimationStart"),transitionend:qi("Transition","TransitionEnd")},ta={},ch={};Rt&&(ch=document.createElement("div").style,"AnimationEvent"in window||(delete qn.animationend.animation,delete qn.animationiteration.animation,delete qn.animationstart.animation),"TransitionEvent"in window||delete qn.transitionend.transition);function ms(e){if(ta[e])return ta[e];if(!qn[e])return e;var t=qn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ch)return ta[e]=t[n];return e}var dh=ms("animationend"),fh=ms("animationiteration"),ph=ms("animationstart"),hh=ms("transitionend"),mh=new Map,td="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function dn(e,t){mh.set(e,t),On(t,[e])}for(var na=0;na<td.length;na++){var ra=td[na],tx=ra.toLowerCase(),nx=ra[0].toUpperCase()+ra.slice(1);dn(tx,"on"+nx)}dn(dh,"onAnimationEnd");dn(fh,"onAnimationIteration");dn(ph,"onAnimationStart");dn("dblclick","onDoubleClick");dn("focusin","onFocus");dn("focusout","onBlur");dn(hh,"onTransitionEnd");mr("onMouseEnter",["mouseout","mouseover"]);mr("onMouseLeave",["mouseout","mouseover"]);mr("onPointerEnter",["pointerout","pointerover"]);mr("onPointerLeave",["pointerout","pointerover"]);On("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));On("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));On("onBeforeInput",["compositionend","keypress","textInput","paste"]);On("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));On("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));On("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Kr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),rx=new Set("cancel close invalid load scroll toggle".split(" ").concat(Kr));function nd(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,t0(r,t,void 0,e),e.currentTarget=null}function gh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==o&&i.isPropagationStopped())break e;nd(i,a,u),o=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==o&&i.isPropagationStopped())break e;nd(i,a,u),o=l}}}if(Ro)throw e=Ya,Ro=!1,Ya=null,e}function J(e,t){var n=t[ol];n===void 0&&(n=t[ol]=new Set);var r=e+"__bubble";n.has(r)||(vh(t,e,2,!1),n.add(r))}function ia(e,t,n){var r=0;t&&(r|=4),vh(n,e,r,t)}var Zi="_reactListening"+Math.random().toString(36).slice(2);function mi(e){if(!e[Zi]){e[Zi]=!0,Pp.forEach(function(n){n!=="selectionchange"&&(rx.has(n)||ia(n,!1,e),ia(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Zi]||(t[Zi]=!0,ia("selectionchange",!1,t))}}function vh(e,t,n,r){switch(eh(t)){case 1:var i=v0;break;case 4:i=y0;break;default:i=pu}n=i.bind(null,t,n,e),i=void 0,!Qa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function oa(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;s=s.return}for(;a!==null;){if(s=bn(a),s===null)return;if(l=s.tag,l===5||l===6){r=o=s;continue e}a=a.parentNode}}r=r.return}zp(function(){var u=o,d=uu(n),c=[];e:{var f=mh.get(e);if(f!==void 0){var g=mu,w=e;switch(e){case"keypress":if(yo(n)===0)break e;case"keydown":case"keyup":g=_0;break;case"focusin":w="focus",g=Zs;break;case"focusout":w="blur",g=Zs;break;case"beforeblur":case"afterblur":g=Zs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Wc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=S0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=V0;break;case dh:case fh:case ph:g=P0;break;case hh:g=O0;break;case"scroll":g=x0;break;case"wheel":g=z0;break;case"copy":case"cut":case"paste":g=E0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Kc}var x=(t&4)!==0,C=!x&&e==="scroll",v=x?f!==null?f+"Capture":null:f;x=[];for(var p=u,m;p!==null;){m=p;var S=m.stateNode;if(m.tag===5&&S!==null&&(m=S,v!==null&&(S=ui(p,v),S!=null&&x.push(gi(p,S,m)))),C)break;p=p.return}0<x.length&&(f=new g(f,w,null,n,d),c.push({event:f,listeners:x}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",f&&n!==Ka&&(w=n.relatedTarget||n.fromElement)&&(bn(w)||w[Mt]))break e;if((g||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,g?(w=n.relatedTarget||n.toElement,g=u,w=w?bn(w):null,w!==null&&(C=Fn(w),w!==C||w.tag!==5&&w.tag!==6)&&(w=null)):(g=null,w=u),g!==w)){if(x=Wc,S="onMouseLeave",v="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(x=Kc,S="onPointerLeave",v="onPointerEnter",p="pointer"),C=g==null?f:Zn(g),m=w==null?f:Zn(w),f=new x(S,p+"leave",g,n,d),f.target=C,f.relatedTarget=m,S=null,bn(d)===u&&(x=new x(v,p+"enter",w,n,d),x.target=m,x.relatedTarget=C,S=x),C=S,g&&w)t:{for(x=g,v=w,p=0,m=x;m;m=Hn(m))p++;for(m=0,S=v;S;S=Hn(S))m++;for(;0<p-m;)x=Hn(x),p--;for(;0<m-p;)v=Hn(v),m--;for(;p--;){if(x===v||v!==null&&x===v.alternate)break t;x=Hn(x),v=Hn(v)}x=null}else x=null;g!==null&&rd(c,f,g,x,!1),w!==null&&C!==null&&rd(c,C,w,x,!0)}}e:{if(f=u?Zn(u):window,g=f.nodeName&&f.nodeName.toLowerCase(),g==="select"||g==="input"&&f.type==="file")var k=G0;else if(Yc(f))if(sh)k=q0;else{k=Y0;var b=Q0}else(g=f.nodeName)&&g.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(k=X0);if(k&&(k=k(e,u))){oh(c,k,n,d);break e}b&&b(e,f,u),e==="focusout"&&(b=f._wrapperState)&&b.controlled&&f.type==="number"&&Ba(f,"number",f.value)}switch(b=u?Zn(u):window,e){case"focusin":(Yc(b)||b.contentEditable==="true")&&(Xn=b,Ja=u,Zr=null);break;case"focusout":Zr=Ja=Xn=null;break;case"mousedown":el=!0;break;case"contextmenu":case"mouseup":case"dragend":el=!1,ed(c,n,d);break;case"selectionchange":if(ex)break;case"keydown":case"keyup":ed(c,n,d)}var P;if(vu)e:{switch(e){case"compositionstart":var E="onCompositionStart";break e;case"compositionend":E="onCompositionEnd";break e;case"compositionupdate":E="onCompositionUpdate";break e}E=void 0}else Yn?rh(e,n)&&(E="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(E="onCompositionStart");E&&(nh&&n.locale!=="ko"&&(Yn||E!=="onCompositionStart"?E==="onCompositionEnd"&&Yn&&(P=th()):(Ht=d,hu="value"in Ht?Ht.value:Ht.textContent,Yn=!0)),b=Do(u,E),0<b.length&&(E=new Hc(E,e,null,n,d),c.push({event:E,listeners:b}),P?E.data=P:(P=ih(n),P!==null&&(E.data=P)))),(P=U0?$0(e,n):W0(e,n))&&(u=Do(u,"onBeforeInput"),0<u.length&&(d=new Hc("onBeforeInput","beforeinput",null,n,d),c.push({event:d,listeners:u}),d.data=P))}gh(c,t)})}function gi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Do(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=ui(e,n),o!=null&&r.unshift(gi(e,o,i)),o=ui(e,t),o!=null&&r.push(gi(e,o,i))),e=e.return}return r}function Hn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function rd(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=ui(n,o),l!=null&&s.unshift(gi(n,l,a))):i||(l=ui(n,o),l!=null&&s.push(gi(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var ix=/\r\n?/g,ox=/\u0000|\uFFFD/g;function id(e){return(typeof e=="string"?e:""+e).replace(ix,`
`).replace(ox,"")}function Ji(e,t,n){if(t=id(t),id(e)!==t&&n)throw Error(T(425))}function Vo(){}var tl=null,nl=null;function rl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var il=typeof setTimeout=="function"?setTimeout:void 0,sx=typeof clearTimeout=="function"?clearTimeout:void 0,od=typeof Promise=="function"?Promise:void 0,ax=typeof queueMicrotask=="function"?queueMicrotask:typeof od<"u"?function(e){return od.resolve(null).then(e).catch(lx)}:il;function lx(e){setTimeout(function(){throw e})}function sa(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),fi(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);fi(t)}function qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function sd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Nr=Math.random().toString(36).slice(2),yt="__reactFiber$"+Nr,vi="__reactProps$"+Nr,Mt="__reactContainer$"+Nr,ol="__reactEvents$"+Nr,ux="__reactListeners$"+Nr,cx="__reactHandles$"+Nr;function bn(e){var t=e[yt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Mt]||n[yt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=sd(e);e!==null;){if(n=e[yt])return n;e=sd(e)}return t}e=n,n=e.parentNode}return null}function _i(e){return e=e[yt]||e[Mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Zn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function gs(e){return e[vi]||null}var sl=[],Jn=-1;function fn(e){return{current:e}}function ee(e){0>Jn||(e.current=sl[Jn],sl[Jn]=null,Jn--)}function X(e,t){Jn++,sl[Jn]=e.current,e.current=t}var ln={},Ne=fn(ln),Oe=fn(!1),An=ln;function gr(e,t){var n=e.type.contextTypes;if(!n)return ln;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Fe(e){return e=e.childContextTypes,e!=null}function Io(){ee(Oe),ee(Ne)}function ad(e,t,n){if(Ne.current!==ln)throw Error(T(168));X(Ne,t),X(Oe,n)}function yh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(T(108,Qy(e)||"Unknown",i));return se({},n,r)}function Oo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ln,An=Ne.current,X(Ne,e),X(Oe,Oe.current),!0}function ld(e,t,n){var r=e.stateNode;if(!r)throw Error(T(169));n?(e=yh(e,t,An),r.__reactInternalMemoizedMergedChildContext=e,ee(Oe),ee(Ne),X(Ne,e)):ee(Oe),X(Oe,n)}var kt=null,vs=!1,aa=!1;function xh(e){kt===null?kt=[e]:kt.push(e)}function dx(e){vs=!0,xh(e)}function pn(){if(!aa&&kt!==null){aa=!0;var e=0,t=K;try{var n=kt;for(K=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}kt=null,vs=!1}catch(i){throw kt!==null&&(kt=kt.slice(e+1)),Wp(cu,pn),i}finally{K=t,aa=!1}}return null}var er=[],tr=0,Fo=null,zo=0,et=[],tt=0,_n=null,Pt=1,bt="";function wn(e,t){er[tr++]=zo,er[tr++]=Fo,Fo=e,zo=t}function wh(e,t,n){et[tt++]=Pt,et[tt++]=bt,et[tt++]=_n,_n=e;var r=Pt;e=bt;var i=32-pt(r)-1;r&=~(1<<i),n+=1;var o=32-pt(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,Pt=1<<32-pt(t)+i|n<<i|r,bt=o+e}else Pt=1<<o|n<<i|r,bt=e}function xu(e){e.return!==null&&(wn(e,1),wh(e,1,0))}function wu(e){for(;e===Fo;)Fo=er[--tr],er[tr]=null,zo=er[--tr],er[tr]=null;for(;e===_n;)_n=et[--tt],et[tt]=null,bt=et[--tt],et[tt]=null,Pt=et[--tt],et[tt]=null}var Ke=null,He=null,te=!1,ft=null;function Sh(e,t){var n=rt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ud(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ke=e,He=qt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ke=e,He=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=_n!==null?{id:Pt,overflow:bt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=rt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ke=e,He=null,!0):!1;default:return!1}}function al(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ll(e){if(te){var t=He;if(t){var n=t;if(!ud(e,t)){if(al(e))throw Error(T(418));t=qt(n.nextSibling);var r=Ke;t&&ud(e,t)?Sh(r,n):(e.flags=e.flags&-4097|2,te=!1,Ke=e)}}else{if(al(e))throw Error(T(418));e.flags=e.flags&-4097|2,te=!1,Ke=e}}}function cd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ke=e}function eo(e){if(e!==Ke)return!1;if(!te)return cd(e),te=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!rl(e.type,e.memoizedProps)),t&&(t=He)){if(al(e))throw Ch(),Error(T(418));for(;t;)Sh(e,t),t=qt(t.nextSibling)}if(cd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){He=qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}He=null}}else He=Ke?qt(e.stateNode.nextSibling):null;return!0}function Ch(){for(var e=He;e;)e=qt(e.nextSibling)}function vr(){He=Ke=null,te=!1}function Su(e){ft===null?ft=[e]:ft.push(e)}var fx=Dt.ReactCurrentBatchConfig;function Vr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var r=n.stateNode}if(!r)throw Error(T(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var a=i.refs;s===null?delete a[o]:a[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function to(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function dd(e){var t=e._init;return t(e._payload)}function kh(e){function t(v,p){if(e){var m=v.deletions;m===null?(v.deletions=[p],v.flags|=16):m.push(p)}}function n(v,p){if(!e)return null;for(;p!==null;)t(v,p),p=p.sibling;return null}function r(v,p){for(v=new Map;p!==null;)p.key!==null?v.set(p.key,p):v.set(p.index,p),p=p.sibling;return v}function i(v,p){return v=tn(v,p),v.index=0,v.sibling=null,v}function o(v,p,m){return v.index=m,e?(m=v.alternate,m!==null?(m=m.index,m<p?(v.flags|=2,p):m):(v.flags|=2,p)):(v.flags|=1048576,p)}function s(v){return e&&v.alternate===null&&(v.flags|=2),v}function a(v,p,m,S){return p===null||p.tag!==6?(p=ha(m,v.mode,S),p.return=v,p):(p=i(p,m),p.return=v,p)}function l(v,p,m,S){var k=m.type;return k===Qn?d(v,p,m.props.children,S,m.key):p!==null&&(p.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===zt&&dd(k)===p.type)?(S=i(p,m.props),S.ref=Vr(v,p,m),S.return=v,S):(S=bo(m.type,m.key,m.props,null,v.mode,S),S.ref=Vr(v,p,m),S.return=v,S)}function u(v,p,m,S){return p===null||p.tag!==4||p.stateNode.containerInfo!==m.containerInfo||p.stateNode.implementation!==m.implementation?(p=ma(m,v.mode,S),p.return=v,p):(p=i(p,m.children||[]),p.return=v,p)}function d(v,p,m,S,k){return p===null||p.tag!==7?(p=Mn(m,v.mode,S,k),p.return=v,p):(p=i(p,m),p.return=v,p)}function c(v,p,m){if(typeof p=="string"&&p!==""||typeof p=="number")return p=ha(""+p,v.mode,m),p.return=v,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Wi:return m=bo(p.type,p.key,p.props,null,v.mode,m),m.ref=Vr(v,null,p),m.return=v,m;case Gn:return p=ma(p,v.mode,m),p.return=v,p;case zt:var S=p._init;return c(v,S(p._payload),m)}if(Wr(p)||Mr(p))return p=Mn(p,v.mode,m,null),p.return=v,p;to(v,p)}return null}function f(v,p,m,S){var k=p!==null?p.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return k!==null?null:a(v,p,""+m,S);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Wi:return m.key===k?l(v,p,m,S):null;case Gn:return m.key===k?u(v,p,m,S):null;case zt:return k=m._init,f(v,p,k(m._payload),S)}if(Wr(m)||Mr(m))return k!==null?null:d(v,p,m,S,null);to(v,m)}return null}function g(v,p,m,S,k){if(typeof S=="string"&&S!==""||typeof S=="number")return v=v.get(m)||null,a(p,v,""+S,k);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Wi:return v=v.get(S.key===null?m:S.key)||null,l(p,v,S,k);case Gn:return v=v.get(S.key===null?m:S.key)||null,u(p,v,S,k);case zt:var b=S._init;return g(v,p,m,b(S._payload),k)}if(Wr(S)||Mr(S))return v=v.get(m)||null,d(p,v,S,k,null);to(p,S)}return null}function w(v,p,m,S){for(var k=null,b=null,P=p,E=p=0,R=null;P!==null&&E<m.length;E++){P.index>E?(R=P,P=null):R=P.sibling;var j=f(v,P,m[E],S);if(j===null){P===null&&(P=R);break}e&&P&&j.alternate===null&&t(v,P),p=o(j,p,E),b===null?k=j:b.sibling=j,b=j,P=R}if(E===m.length)return n(v,P),te&&wn(v,E),k;if(P===null){for(;E<m.length;E++)P=c(v,m[E],S),P!==null&&(p=o(P,p,E),b===null?k=P:b.sibling=P,b=P);return te&&wn(v,E),k}for(P=r(v,P);E<m.length;E++)R=g(P,v,E,m[E],S),R!==null&&(e&&R.alternate!==null&&P.delete(R.key===null?E:R.key),p=o(R,p,E),b===null?k=R:b.sibling=R,b=R);return e&&P.forEach(function(O){return t(v,O)}),te&&wn(v,E),k}function x(v,p,m,S){var k=Mr(m);if(typeof k!="function")throw Error(T(150));if(m=k.call(m),m==null)throw Error(T(151));for(var b=k=null,P=p,E=p=0,R=null,j=m.next();P!==null&&!j.done;E++,j=m.next()){P.index>E?(R=P,P=null):R=P.sibling;var O=f(v,P,j.value,S);if(O===null){P===null&&(P=R);break}e&&P&&O.alternate===null&&t(v,P),p=o(O,p,E),b===null?k=O:b.sibling=O,b=O,P=R}if(j.done)return n(v,P),te&&wn(v,E),k;if(P===null){for(;!j.done;E++,j=m.next())j=c(v,j.value,S),j!==null&&(p=o(j,p,E),b===null?k=j:b.sibling=j,b=j);return te&&wn(v,E),k}for(P=r(v,P);!j.done;E++,j=m.next())j=g(P,v,E,j.value,S),j!==null&&(e&&j.alternate!==null&&P.delete(j.key===null?E:j.key),p=o(j,p,E),b===null?k=j:b.sibling=j,b=j);return e&&P.forEach(function(D){return t(v,D)}),te&&wn(v,E),k}function C(v,p,m,S){if(typeof m=="object"&&m!==null&&m.type===Qn&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case Wi:e:{for(var k=m.key,b=p;b!==null;){if(b.key===k){if(k=m.type,k===Qn){if(b.tag===7){n(v,b.sibling),p=i(b,m.props.children),p.return=v,v=p;break e}}else if(b.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===zt&&dd(k)===b.type){n(v,b.sibling),p=i(b,m.props),p.ref=Vr(v,b,m),p.return=v,v=p;break e}n(v,b);break}else t(v,b);b=b.sibling}m.type===Qn?(p=Mn(m.props.children,v.mode,S,m.key),p.return=v,v=p):(S=bo(m.type,m.key,m.props,null,v.mode,S),S.ref=Vr(v,p,m),S.return=v,v=S)}return s(v);case Gn:e:{for(b=m.key;p!==null;){if(p.key===b)if(p.tag===4&&p.stateNode.containerInfo===m.containerInfo&&p.stateNode.implementation===m.implementation){n(v,p.sibling),p=i(p,m.children||[]),p.return=v,v=p;break e}else{n(v,p);break}else t(v,p);p=p.sibling}p=ma(m,v.mode,S),p.return=v,v=p}return s(v);case zt:return b=m._init,C(v,p,b(m._payload),S)}if(Wr(m))return w(v,p,m,S);if(Mr(m))return x(v,p,m,S);to(v,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,p!==null&&p.tag===6?(n(v,p.sibling),p=i(p,m),p.return=v,v=p):(n(v,p),p=ha(m,v.mode,S),p.return=v,v=p),s(v)):n(v,p)}return C}var yr=kh(!0),Ph=kh(!1),Bo=fn(null),Uo=null,nr=null,Cu=null;function ku(){Cu=nr=Uo=null}function Pu(e){var t=Bo.current;ee(Bo),e._currentValue=t}function ul(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function pr(e,t){Uo=e,Cu=nr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ie=!0),e.firstContext=null)}function ot(e){var t=e._currentValue;if(Cu!==e)if(e={context:e,memoizedValue:t,next:null},nr===null){if(Uo===null)throw Error(T(308));nr=e,Uo.dependencies={lanes:0,firstContext:e}}else nr=nr.next=e;return t}var En=null;function bu(e){En===null?En=[e]:En.push(e)}function bh(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,bu(t)):(n.next=i.next,i.next=n),t.interleaved=n,At(e,r)}function At(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Bt=!1;function Eu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Eh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Tt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Zt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,W&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,At(e,n)}return i=r.interleaved,i===null?(t.next=t,bu(r)):(t.next=i.next,i.next=t),r.interleaved=t,At(e,n)}function xo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,du(e,n)}}function fd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function $o(e,t,n,r){var i=e.updateQueue;Bt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?o=u:s.next=u,s=l;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==s&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(o!==null){var c=i.baseState;s=0,d=u=l=null,a=o;do{var f=a.lane,g=a.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:g,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var w=e,x=a;switch(f=t,g=n,x.tag){case 1:if(w=x.payload,typeof w=="function"){c=w.call(g,c,f);break e}c=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=x.payload,f=typeof w=="function"?w.call(g,c,f):w,f==null)break e;c=se({},c,f);break e;case 2:Bt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=i.effects,f===null?i.effects=[a]:f.push(a))}else g={eventTime:g,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=g,l=c):d=d.next=g,s|=f;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;f=a,a=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(1);if(d===null&&(l=c),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=d,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);Dn|=s,e.lanes=s,e.memoizedState=c}}function pd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(T(191,i));i.call(r)}}}var Li={},wt=fn(Li),yi=fn(Li),xi=fn(Li);function Tn(e){if(e===Li)throw Error(T(174));return e}function Tu(e,t){switch(X(xi,t),X(yi,e),X(wt,Li),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:$a(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=$a(t,e)}ee(wt),X(wt,t)}function xr(){ee(wt),ee(yi),ee(xi)}function Th(e){Tn(xi.current);var t=Tn(wt.current),n=$a(t,e.type);t!==n&&(X(yi,e),X(wt,n))}function Nu(e){yi.current===e&&(ee(wt),ee(yi))}var ne=fn(0);function Wo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var la=[];function ju(){for(var e=0;e<la.length;e++)la[e]._workInProgressVersionPrimary=null;la.length=0}var wo=Dt.ReactCurrentDispatcher,ua=Dt.ReactCurrentBatchConfig,Ln=0,oe=null,ge=null,xe=null,Ho=!1,Jr=!1,wi=0,px=0;function Pe(){throw Error(T(321))}function Ru(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!mt(e[n],t[n]))return!1;return!0}function Mu(e,t,n,r,i,o){if(Ln=o,oe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,wo.current=e===null||e.memoizedState===null?vx:yx,e=n(r,i),Jr){o=0;do{if(Jr=!1,wi=0,25<=o)throw Error(T(301));o+=1,xe=ge=null,t.updateQueue=null,wo.current=xx,e=n(r,i)}while(Jr)}if(wo.current=Ko,t=ge!==null&&ge.next!==null,Ln=0,xe=ge=oe=null,Ho=!1,t)throw Error(T(300));return e}function Au(){var e=wi!==0;return wi=0,e}function vt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return xe===null?oe.memoizedState=xe=e:xe=xe.next=e,xe}function st(){if(ge===null){var e=oe.alternate;e=e!==null?e.memoizedState:null}else e=ge.next;var t=xe===null?oe.memoizedState:xe.next;if(t!==null)xe=t,ge=e;else{if(e===null)throw Error(T(310));ge=e,e={memoizedState:ge.memoizedState,baseState:ge.baseState,baseQueue:ge.baseQueue,queue:ge.queue,next:null},xe===null?oe.memoizedState=xe=e:xe=xe.next=e}return xe}function Si(e,t){return typeof t=="function"?t(e):t}function ca(e){var t=st(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=ge,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var a=s=null,l=null,u=o;do{var d=u.lane;if((Ln&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var c={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=c,s=r):l=l.next=c,oe.lanes|=d,Dn|=d}u=u.next}while(u!==null&&u!==o);l===null?s=r:l.next=a,mt(r,t.memoizedState)||(Ie=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,oe.lanes|=o,Dn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function da(e){var t=st(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);mt(o,t.memoizedState)||(Ie=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Nh(){}function jh(e,t){var n=oe,r=st(),i=t(),o=!mt(r.memoizedState,i);if(o&&(r.memoizedState=i,Ie=!0),r=r.queue,_u(Ah.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||xe!==null&&xe.memoizedState.tag&1){if(n.flags|=2048,Ci(9,Mh.bind(null,n,r,i,t),void 0,null),we===null)throw Error(T(349));Ln&30||Rh(n,t,i)}return i}function Rh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=oe.updateQueue,t===null?(t={lastEffect:null,stores:null},oe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Mh(e,t,n,r){t.value=n,t.getSnapshot=r,_h(t)&&Lh(e)}function Ah(e,t,n){return n(function(){_h(t)&&Lh(e)})}function _h(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!mt(e,n)}catch{return!0}}function Lh(e){var t=At(e,1);t!==null&&ht(t,e,1,-1)}function hd(e){var t=vt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Si,lastRenderedState:e},t.queue=e,e=e.dispatch=gx.bind(null,oe,e),[t.memoizedState,e]}function Ci(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=oe.updateQueue,t===null?(t={lastEffect:null,stores:null},oe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Dh(){return st().memoizedState}function So(e,t,n,r){var i=vt();oe.flags|=e,i.memoizedState=Ci(1|t,n,void 0,r===void 0?null:r)}function ys(e,t,n,r){var i=st();r=r===void 0?null:r;var o=void 0;if(ge!==null){var s=ge.memoizedState;if(o=s.destroy,r!==null&&Ru(r,s.deps)){i.memoizedState=Ci(t,n,o,r);return}}oe.flags|=e,i.memoizedState=Ci(1|t,n,o,r)}function md(e,t){return So(8390656,8,e,t)}function _u(e,t){return ys(2048,8,e,t)}function Vh(e,t){return ys(4,2,e,t)}function Ih(e,t){return ys(4,4,e,t)}function Oh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Fh(e,t,n){return n=n!=null?n.concat([e]):null,ys(4,4,Oh.bind(null,t,e),n)}function Lu(){}function zh(e,t){var n=st();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ru(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Bh(e,t){var n=st();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ru(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Uh(e,t,n){return Ln&21?(mt(n,t)||(n=Gp(),oe.lanes|=n,Dn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ie=!0),e.memoizedState=n)}function hx(e,t){var n=K;K=n!==0&&4>n?n:4,e(!0);var r=ua.transition;ua.transition={};try{e(!1),t()}finally{K=n,ua.transition=r}}function $h(){return st().memoizedState}function mx(e,t,n){var r=en(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Wh(e))Hh(t,n);else if(n=bh(e,t,n,r),n!==null){var i=_e();ht(n,e,r,i),Kh(n,t,r)}}function gx(e,t,n){var r=en(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Wh(e))Hh(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,a=o(s,n);if(i.hasEagerState=!0,i.eagerState=a,mt(a,s)){var l=t.interleaved;l===null?(i.next=i,bu(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=bh(e,t,i,r),n!==null&&(i=_e(),ht(n,e,r,i),Kh(n,t,r))}}function Wh(e){var t=e.alternate;return e===oe||t!==null&&t===oe}function Hh(e,t){Jr=Ho=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Kh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,du(e,n)}}var Ko={readContext:ot,useCallback:Pe,useContext:Pe,useEffect:Pe,useImperativeHandle:Pe,useInsertionEffect:Pe,useLayoutEffect:Pe,useMemo:Pe,useReducer:Pe,useRef:Pe,useState:Pe,useDebugValue:Pe,useDeferredValue:Pe,useTransition:Pe,useMutableSource:Pe,useSyncExternalStore:Pe,useId:Pe,unstable_isNewReconciler:!1},vx={readContext:ot,useCallback:function(e,t){return vt().memoizedState=[e,t===void 0?null:t],e},useContext:ot,useEffect:md,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,So(4194308,4,Oh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return So(4194308,4,e,t)},useInsertionEffect:function(e,t){return So(4,2,e,t)},useMemo:function(e,t){var n=vt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=mx.bind(null,oe,e),[r.memoizedState,e]},useRef:function(e){var t=vt();return e={current:e},t.memoizedState=e},useState:hd,useDebugValue:Lu,useDeferredValue:function(e){return vt().memoizedState=e},useTransition:function(){var e=hd(!1),t=e[0];return e=hx.bind(null,e[1]),vt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=oe,i=vt();if(te){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),we===null)throw Error(T(349));Ln&30||Rh(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,md(Ah.bind(null,r,o,e),[e]),r.flags|=2048,Ci(9,Mh.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=vt(),t=we.identifierPrefix;if(te){var n=bt,r=Pt;n=(r&~(1<<32-pt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=wi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=px++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},yx={readContext:ot,useCallback:zh,useContext:ot,useEffect:_u,useImperativeHandle:Fh,useInsertionEffect:Vh,useLayoutEffect:Ih,useMemo:Bh,useReducer:ca,useRef:Dh,useState:function(){return ca(Si)},useDebugValue:Lu,useDeferredValue:function(e){var t=st();return Uh(t,ge.memoizedState,e)},useTransition:function(){var e=ca(Si)[0],t=st().memoizedState;return[e,t]},useMutableSource:Nh,useSyncExternalStore:jh,useId:$h,unstable_isNewReconciler:!1},xx={readContext:ot,useCallback:zh,useContext:ot,useEffect:_u,useImperativeHandle:Fh,useInsertionEffect:Vh,useLayoutEffect:Ih,useMemo:Bh,useReducer:da,useRef:Dh,useState:function(){return da(Si)},useDebugValue:Lu,useDeferredValue:function(e){var t=st();return ge===null?t.memoizedState=e:Uh(t,ge.memoizedState,e)},useTransition:function(){var e=da(Si)[0],t=st().memoizedState;return[e,t]},useMutableSource:Nh,useSyncExternalStore:jh,useId:$h,unstable_isNewReconciler:!1};function ct(e,t){if(e&&e.defaultProps){t=se({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function cl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:se({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var xs={isMounted:function(e){return(e=e._reactInternals)?Fn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=_e(),i=en(e),o=Tt(r,i);o.payload=t,n!=null&&(o.callback=n),t=Zt(e,o,i),t!==null&&(ht(t,e,i,r),xo(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=_e(),i=en(e),o=Tt(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Zt(e,o,i),t!==null&&(ht(t,e,i,r),xo(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=_e(),r=en(e),i=Tt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Zt(e,i,r),t!==null&&(ht(t,e,r,n),xo(t,e,r))}};function gd(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!hi(n,r)||!hi(i,o):!0}function Gh(e,t,n){var r=!1,i=ln,o=t.contextType;return typeof o=="object"&&o!==null?o=ot(o):(i=Fe(t)?An:Ne.current,r=t.contextTypes,o=(r=r!=null)?gr(e,i):ln),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=xs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function vd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&xs.enqueueReplaceState(t,t.state,null)}function dl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Eu(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=ot(o):(o=Fe(t)?An:Ne.current,i.context=gr(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(cl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&xs.enqueueReplaceState(i,i.state,null),$o(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function wr(e,t){try{var n="",r=t;do n+=Gy(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function fa(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function fl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var wx=typeof WeakMap=="function"?WeakMap:Map;function Qh(e,t,n){n=Tt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Qo||(Qo=!0,Cl=r),fl(e,t)},n}function Yh(e,t,n){n=Tt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){fl(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){fl(e,t),typeof r!="function"&&(Jt===null?Jt=new Set([this]):Jt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function yd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new wx;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Lx.bind(null,e,t,n),t.then(e,e))}function xd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function wd(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Tt(-1,1),t.tag=2,Zt(n,t,1))),n.lanes|=1),e)}var Sx=Dt.ReactCurrentOwner,Ie=!1;function Ae(e,t,n,r){t.child=e===null?Ph(t,null,n,r):yr(t,e.child,n,r)}function Sd(e,t,n,r,i){n=n.render;var o=t.ref;return pr(t,i),r=Mu(e,t,n,r,o,i),n=Au(),e!==null&&!Ie?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,_t(e,t,i)):(te&&n&&xu(t),t.flags|=1,Ae(e,t,r,i),t.child)}function Cd(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!Uu(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Xh(e,t,o,r,i)):(e=bo(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:hi,n(s,r)&&e.ref===t.ref)return _t(e,t,i)}return t.flags|=1,e=tn(o,r),e.ref=t.ref,e.return=t,t.child=e}function Xh(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(hi(o,r)&&e.ref===t.ref)if(Ie=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Ie=!0);else return t.lanes=e.lanes,_t(e,t,i)}return pl(e,t,n,r,i)}function qh(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},X(ir,We),We|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,X(ir,We),We|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,X(ir,We),We|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,X(ir,We),We|=r;return Ae(e,t,i,n),t.child}function Zh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function pl(e,t,n,r,i){var o=Fe(n)?An:Ne.current;return o=gr(t,o),pr(t,i),n=Mu(e,t,n,r,o,i),r=Au(),e!==null&&!Ie?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,_t(e,t,i)):(te&&r&&xu(t),t.flags|=1,Ae(e,t,n,i),t.child)}function kd(e,t,n,r,i){if(Fe(n)){var o=!0;Oo(t)}else o=!1;if(pr(t,i),t.stateNode===null)Co(e,t),Gh(t,n,r),dl(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=ot(u):(u=Fe(n)?An:Ne.current,u=gr(t,u));var d=n.getDerivedStateFromProps,c=typeof d=="function"||typeof s.getSnapshotBeforeUpdate=="function";c||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&vd(t,s,r,u),Bt=!1;var f=t.memoizedState;s.state=f,$o(t,r,s,i),l=t.memoizedState,a!==r||f!==l||Oe.current||Bt?(typeof d=="function"&&(cl(t,n,d,r),l=t.memoizedState),(a=Bt||gd(t,n,a,r,f,l,u))?(c||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Eh(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:ct(t.type,a),s.props=u,c=t.pendingProps,f=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=ot(l):(l=Fe(n)?An:Ne.current,l=gr(t,l));var g=n.getDerivedStateFromProps;(d=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==c||f!==l)&&vd(t,s,r,l),Bt=!1,f=t.memoizedState,s.state=f,$o(t,r,s,i);var w=t.memoizedState;a!==c||f!==w||Oe.current||Bt?(typeof g=="function"&&(cl(t,n,g,r),w=t.memoizedState),(u=Bt||gd(t,n,u,r,f,w,l)||!1)?(d||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,w,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,w,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),s.props=r,s.state=w,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return hl(e,t,n,r,o,i)}function hl(e,t,n,r,i,o){Zh(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&ld(t,n,!1),_t(e,t,o);r=t.stateNode,Sx.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=yr(t,e.child,null,o),t.child=yr(t,null,a,o)):Ae(e,t,a,o),t.memoizedState=r.state,i&&ld(t,n,!0),t.child}function Jh(e){var t=e.stateNode;t.pendingContext?ad(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ad(e,t.context,!1),Tu(e,t.containerInfo)}function Pd(e,t,n,r,i){return vr(),Su(i),t.flags|=256,Ae(e,t,n,r),t.child}var ml={dehydrated:null,treeContext:null,retryLane:0};function gl(e){return{baseLanes:e,cachePool:null,transitions:null}}function em(e,t,n){var r=t.pendingProps,i=ne.current,o=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),X(ne,i&1),e===null)return ll(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Cs(s,r,0,null),e=Mn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=gl(n),t.memoizedState=ml,e):Du(t,s));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return Cx(e,t,s,r,a,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=tn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?o=tn(a,o):(o=Mn(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?gl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=ml,r}return o=e.child,e=o.sibling,r=tn(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Du(e,t){return t=Cs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function no(e,t,n,r){return r!==null&&Su(r),yr(t,e.child,null,n),e=Du(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Cx(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=fa(Error(T(422))),no(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Cs({mode:"visible",children:r.children},i,0,null),o=Mn(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&yr(t,e.child,null,s),t.child.memoizedState=gl(s),t.memoizedState=ml,o);if(!(t.mode&1))return no(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(T(419)),r=fa(o,r,void 0),no(e,t,s,r)}if(a=(s&e.childLanes)!==0,Ie||a){if(r=we,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,At(e,i),ht(r,e,i,-1))}return Bu(),r=fa(Error(T(421))),no(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Dx.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,He=qt(i.nextSibling),Ke=t,te=!0,ft=null,e!==null&&(et[tt++]=Pt,et[tt++]=bt,et[tt++]=_n,Pt=e.id,bt=e.overflow,_n=t),t=Du(t,r.children),t.flags|=4096,t)}function bd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ul(e.return,t,n)}function pa(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function tm(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(Ae(e,t,r.children,n),r=ne.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&bd(e,n,t);else if(e.tag===19)bd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(X(ne,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Wo(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),pa(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Wo(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}pa(t,!0,n,null,o);break;case"together":pa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Co(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function _t(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Dn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=tn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=tn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function kx(e,t,n){switch(t.tag){case 3:Jh(t),vr();break;case 5:Th(t);break;case 1:Fe(t.type)&&Oo(t);break;case 4:Tu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;X(Bo,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(X(ne,ne.current&1),t.flags|=128,null):n&t.child.childLanes?em(e,t,n):(X(ne,ne.current&1),e=_t(e,t,n),e!==null?e.sibling:null);X(ne,ne.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return tm(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),X(ne,ne.current),r)break;return null;case 22:case 23:return t.lanes=0,qh(e,t,n)}return _t(e,t,n)}var nm,vl,rm,im;nm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};vl=function(){};rm=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Tn(wt.current);var o=null;switch(n){case"input":i=Fa(e,i),r=Fa(e,r),o=[];break;case"select":i=se({},i,{value:void 0}),r=se({},r,{value:void 0}),o=[];break;case"textarea":i=Ua(e,i),r=Ua(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Vo)}Wa(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(ai.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(o||(o=[]),o.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(o=o||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(ai.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&J("scroll",e),o||a===l||(o=[])):(o=o||[]).push(u,l))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};im=function(e,t,n,r){n!==r&&(t.flags|=4)};function Ir(e,t){if(!te)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function be(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Px(e,t,n){var r=t.pendingProps;switch(wu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return be(t),null;case 1:return Fe(t.type)&&Io(),be(t),null;case 3:return r=t.stateNode,xr(),ee(Oe),ee(Ne),ju(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(eo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ft!==null&&(bl(ft),ft=null))),vl(e,t),be(t),null;case 5:Nu(t);var i=Tn(xi.current);if(n=t.type,e!==null&&t.stateNode!=null)rm(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(T(166));return be(t),null}if(e=Tn(wt.current),eo(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[yt]=t,r[vi]=o,e=(t.mode&1)!==0,n){case"dialog":J("cancel",r),J("close",r);break;case"iframe":case"object":case"embed":J("load",r);break;case"video":case"audio":for(i=0;i<Kr.length;i++)J(Kr[i],r);break;case"source":J("error",r);break;case"img":case"image":case"link":J("error",r),J("load",r);break;case"details":J("toggle",r);break;case"input":Lc(r,o),J("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},J("invalid",r);break;case"textarea":Vc(r,o),J("invalid",r)}Wa(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var a=o[s];s==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&Ji(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&Ji(r.textContent,a,e),i=["children",""+a]):ai.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&J("scroll",r)}switch(n){case"input":Hi(r),Dc(r,o,!0);break;case"textarea":Hi(r),Ic(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Vo)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ap(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[yt]=t,e[vi]=r,nm(e,t,!1,!1),t.stateNode=e;e:{switch(s=Ha(n,r),n){case"dialog":J("cancel",e),J("close",e),i=r;break;case"iframe":case"object":case"embed":J("load",e),i=r;break;case"video":case"audio":for(i=0;i<Kr.length;i++)J(Kr[i],e);i=r;break;case"source":J("error",e),i=r;break;case"img":case"image":case"link":J("error",e),J("load",e),i=r;break;case"details":J("toggle",e),i=r;break;case"input":Lc(e,r),i=Fa(e,r),J("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=se({},r,{value:void 0}),J("invalid",e);break;case"textarea":Vc(e,r),i=Ua(e,r),J("invalid",e);break;default:i=r}Wa(n,i),a=i;for(o in a)if(a.hasOwnProperty(o)){var l=a[o];o==="style"?Dp(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&_p(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&li(e,l):typeof l=="number"&&li(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(ai.hasOwnProperty(o)?l!=null&&o==="onScroll"&&J("scroll",e):l!=null&&ou(e,o,l,s))}switch(n){case"input":Hi(e),Dc(e,r,!1);break;case"textarea":Hi(e),Ic(e);break;case"option":r.value!=null&&e.setAttribute("value",""+an(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?ur(e,!!r.multiple,o,!1):r.defaultValue!=null&&ur(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Vo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return be(t),null;case 6:if(e&&t.stateNode!=null)im(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(T(166));if(n=Tn(xi.current),Tn(wt.current),eo(t)){if(r=t.stateNode,n=t.memoizedProps,r[yt]=t,(o=r.nodeValue!==n)&&(e=Ke,e!==null))switch(e.tag){case 3:Ji(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ji(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[yt]=t,t.stateNode=r}return be(t),null;case 13:if(ee(ne),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(te&&He!==null&&t.mode&1&&!(t.flags&128))Ch(),vr(),t.flags|=98560,o=!1;else if(o=eo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(T(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(T(317));o[yt]=t}else vr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;be(t),o=!1}else ft!==null&&(bl(ft),ft=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ne.current&1?ve===0&&(ve=3):Bu())),t.updateQueue!==null&&(t.flags|=4),be(t),null);case 4:return xr(),vl(e,t),e===null&&mi(t.stateNode.containerInfo),be(t),null;case 10:return Pu(t.type._context),be(t),null;case 17:return Fe(t.type)&&Io(),be(t),null;case 19:if(ee(ne),o=t.memoizedState,o===null)return be(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)Ir(o,!1);else{if(ve!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Wo(e),s!==null){for(t.flags|=128,Ir(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return X(ne,ne.current&1|2),t.child}e=e.sibling}o.tail!==null&&de()>Sr&&(t.flags|=128,r=!0,Ir(o,!1),t.lanes=4194304)}else{if(!r)if(e=Wo(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Ir(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!te)return be(t),null}else 2*de()-o.renderingStartTime>Sr&&n!==1073741824&&(t.flags|=128,r=!0,Ir(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=de(),t.sibling=null,n=ne.current,X(ne,r?n&1|2:n&1),t):(be(t),null);case 22:case 23:return zu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?We&1073741824&&(be(t),t.subtreeFlags&6&&(t.flags|=8192)):be(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function bx(e,t){switch(wu(t),t.tag){case 1:return Fe(t.type)&&Io(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return xr(),ee(Oe),ee(Ne),ju(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Nu(t),null;case 13:if(ee(ne),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));vr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ee(ne),null;case 4:return xr(),null;case 10:return Pu(t.type._context),null;case 22:case 23:return zu(),null;case 24:return null;default:return null}}var ro=!1,Te=!1,Ex=typeof WeakSet=="function"?WeakSet:Set,M=null;function rr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ae(e,t,r)}else n.current=null}function yl(e,t,n){try{n()}catch(r){ae(e,t,r)}}var Ed=!1;function Tx(e,t){if(tl=_o,e=uh(),yu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,d=0,c=e,f=null;t:for(;;){for(var g;c!==n||i!==0&&c.nodeType!==3||(a=s+i),c!==o||r!==0&&c.nodeType!==3||(l=s+r),c.nodeType===3&&(s+=c.nodeValue.length),(g=c.firstChild)!==null;)f=c,c=g;for(;;){if(c===e)break t;if(f===n&&++u===i&&(a=s),f===o&&++d===r&&(l=s),(g=c.nextSibling)!==null)break;c=f,f=c.parentNode}c=g}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(nl={focusedElem:e,selectionRange:n},_o=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var x=w.memoizedProps,C=w.memoizedState,v=t.stateNode,p=v.getSnapshotBeforeUpdate(t.elementType===t.type?x:ct(t.type,x),C);v.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(S){ae(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return w=Ed,Ed=!1,w}function ei(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&yl(t,n,o)}i=i.next}while(i!==r)}}function ws(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function xl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function om(e){var t=e.alternate;t!==null&&(e.alternate=null,om(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[yt],delete t[vi],delete t[ol],delete t[ux],delete t[cx])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function sm(e){return e.tag===5||e.tag===3||e.tag===4}function Td(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||sm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function wl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Vo));else if(r!==4&&(e=e.child,e!==null))for(wl(e,t,n),e=e.sibling;e!==null;)wl(e,t,n),e=e.sibling}function Sl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Sl(e,t,n),e=e.sibling;e!==null;)Sl(e,t,n),e=e.sibling}var Se=null,dt=!1;function Vt(e,t,n){for(n=n.child;n!==null;)am(e,t,n),n=n.sibling}function am(e,t,n){if(xt&&typeof xt.onCommitFiberUnmount=="function")try{xt.onCommitFiberUnmount(fs,n)}catch{}switch(n.tag){case 5:Te||rr(n,t);case 6:var r=Se,i=dt;Se=null,Vt(e,t,n),Se=r,dt=i,Se!==null&&(dt?(e=Se,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Se.removeChild(n.stateNode));break;case 18:Se!==null&&(dt?(e=Se,n=n.stateNode,e.nodeType===8?sa(e.parentNode,n):e.nodeType===1&&sa(e,n),fi(e)):sa(Se,n.stateNode));break;case 4:r=Se,i=dt,Se=n.stateNode.containerInfo,dt=!0,Vt(e,t,n),Se=r,dt=i;break;case 0:case 11:case 14:case 15:if(!Te&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&yl(n,t,s),i=i.next}while(i!==r)}Vt(e,t,n);break;case 1:if(!Te&&(rr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ae(n,t,a)}Vt(e,t,n);break;case 21:Vt(e,t,n);break;case 22:n.mode&1?(Te=(r=Te)||n.memoizedState!==null,Vt(e,t,n),Te=r):Vt(e,t,n);break;default:Vt(e,t,n)}}function Nd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Ex),t.forEach(function(r){var i=Vx.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function at(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:Se=a.stateNode,dt=!1;break e;case 3:Se=a.stateNode.containerInfo,dt=!0;break e;case 4:Se=a.stateNode.containerInfo,dt=!0;break e}a=a.return}if(Se===null)throw Error(T(160));am(o,s,i),Se=null,dt=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){ae(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)lm(t,e),t=t.sibling}function lm(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(at(t,e),gt(e),r&4){try{ei(3,e,e.return),ws(3,e)}catch(x){ae(e,e.return,x)}try{ei(5,e,e.return)}catch(x){ae(e,e.return,x)}}break;case 1:at(t,e),gt(e),r&512&&n!==null&&rr(n,n.return);break;case 5:if(at(t,e),gt(e),r&512&&n!==null&&rr(n,n.return),e.flags&32){var i=e.stateNode;try{li(i,"")}catch(x){ae(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&Rp(i,o),Ha(a,s);var u=Ha(a,o);for(s=0;s<l.length;s+=2){var d=l[s],c=l[s+1];d==="style"?Dp(i,c):d==="dangerouslySetInnerHTML"?_p(i,c):d==="children"?li(i,c):ou(i,d,c,u)}switch(a){case"input":za(i,o);break;case"textarea":Mp(i,o);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var g=o.value;g!=null?ur(i,!!o.multiple,g,!1):f!==!!o.multiple&&(o.defaultValue!=null?ur(i,!!o.multiple,o.defaultValue,!0):ur(i,!!o.multiple,o.multiple?[]:"",!1))}i[vi]=o}catch(x){ae(e,e.return,x)}}break;case 6:if(at(t,e),gt(e),r&4){if(e.stateNode===null)throw Error(T(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(x){ae(e,e.return,x)}}break;case 3:if(at(t,e),gt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{fi(t.containerInfo)}catch(x){ae(e,e.return,x)}break;case 4:at(t,e),gt(e);break;case 13:at(t,e),gt(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(Ou=de())),r&4&&Nd(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(Te=(u=Te)||d,at(t,e),Te=u):at(t,e),gt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(M=e,d=e.child;d!==null;){for(c=M=d;M!==null;){switch(f=M,g=f.child,f.tag){case 0:case 11:case 14:case 15:ei(4,f,f.return);break;case 1:rr(f,f.return);var w=f.stateNode;if(typeof w.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(x){ae(r,n,x)}}break;case 5:rr(f,f.return);break;case 22:if(f.memoizedState!==null){Rd(c);continue}}g!==null?(g.return=f,M=g):Rd(c)}d=d.sibling}e:for(d=null,c=e;;){if(c.tag===5){if(d===null){d=c;try{i=c.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=c.stateNode,l=c.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Lp("display",s))}catch(x){ae(e,e.return,x)}}}else if(c.tag===6){if(d===null)try{c.stateNode.nodeValue=u?"":c.memoizedProps}catch(x){ae(e,e.return,x)}}else if((c.tag!==22&&c.tag!==23||c.memoizedState===null||c===e)&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===e)break e;for(;c.sibling===null;){if(c.return===null||c.return===e)break e;d===c&&(d=null),c=c.return}d===c&&(d=null),c.sibling.return=c.return,c=c.sibling}}break;case 19:at(t,e),gt(e),r&4&&Nd(e);break;case 21:break;default:at(t,e),gt(e)}}function gt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(sm(n)){var r=n;break e}n=n.return}throw Error(T(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(li(i,""),r.flags&=-33);var o=Td(e);Sl(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,a=Td(e);wl(e,a,s);break;default:throw Error(T(161))}}catch(l){ae(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Nx(e,t,n){M=e,um(e)}function um(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var i=M,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||ro;if(!s){var a=i.alternate,l=a!==null&&a.memoizedState!==null||Te;a=ro;var u=Te;if(ro=s,(Te=l)&&!u)for(M=i;M!==null;)s=M,l=s.child,s.tag===22&&s.memoizedState!==null?Md(i):l!==null?(l.return=s,M=l):Md(i);for(;o!==null;)M=o,um(o),o=o.sibling;M=i,ro=a,Te=u}jd(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,M=o):jd(e)}}function jd(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Te||ws(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Te)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:ct(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&pd(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}pd(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var c=d.dehydrated;c!==null&&fi(c)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}Te||t.flags&512&&xl(t)}catch(f){ae(t,t.return,f)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Rd(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Md(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ws(4,t)}catch(l){ae(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){ae(t,i,l)}}var o=t.return;try{xl(t)}catch(l){ae(t,o,l)}break;case 5:var s=t.return;try{xl(t)}catch(l){ae(t,s,l)}}}catch(l){ae(t,t.return,l)}if(t===e){M=null;break}var a=t.sibling;if(a!==null){a.return=t.return,M=a;break}M=t.return}}var jx=Math.ceil,Go=Dt.ReactCurrentDispatcher,Vu=Dt.ReactCurrentOwner,it=Dt.ReactCurrentBatchConfig,W=0,we=null,me=null,Ce=0,We=0,ir=fn(0),ve=0,ki=null,Dn=0,Ss=0,Iu=0,ti=null,Ve=null,Ou=0,Sr=1/0,Ct=null,Qo=!1,Cl=null,Jt=null,io=!1,Kt=null,Yo=0,ni=0,kl=null,ko=-1,Po=0;function _e(){return W&6?de():ko!==-1?ko:ko=de()}function en(e){return e.mode&1?W&2&&Ce!==0?Ce&-Ce:fx.transition!==null?(Po===0&&(Po=Gp()),Po):(e=K,e!==0||(e=window.event,e=e===void 0?16:eh(e.type)),e):1}function ht(e,t,n,r){if(50<ni)throw ni=0,kl=null,Error(T(185));Mi(e,n,r),(!(W&2)||e!==we)&&(e===we&&(!(W&2)&&(Ss|=n),ve===4&&Wt(e,Ce)),ze(e,r),n===1&&W===0&&!(t.mode&1)&&(Sr=de()+500,vs&&pn()))}function ze(e,t){var n=e.callbackNode;f0(e,t);var r=Ao(e,e===we?Ce:0);if(r===0)n!==null&&zc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&zc(n),t===1)e.tag===0?dx(Ad.bind(null,e)):xh(Ad.bind(null,e)),ax(function(){!(W&6)&&pn()}),n=null;else{switch(Qp(r)){case 1:n=cu;break;case 4:n=Hp;break;case 16:n=Mo;break;case 536870912:n=Kp;break;default:n=Mo}n=vm(n,cm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function cm(e,t){if(ko=-1,Po=0,W&6)throw Error(T(327));var n=e.callbackNode;if(hr()&&e.callbackNode!==n)return null;var r=Ao(e,e===we?Ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Xo(e,r);else{t=r;var i=W;W|=2;var o=fm();(we!==e||Ce!==t)&&(Ct=null,Sr=de()+500,Rn(e,t));do try{Ax();break}catch(a){dm(e,a)}while(1);ku(),Go.current=o,W=i,me!==null?t=0:(we=null,Ce=0,t=ve)}if(t!==0){if(t===2&&(i=Xa(e),i!==0&&(r=i,t=Pl(e,i))),t===1)throw n=ki,Rn(e,0),Wt(e,r),ze(e,de()),n;if(t===6)Wt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Rx(i)&&(t=Xo(e,r),t===2&&(o=Xa(e),o!==0&&(r=o,t=Pl(e,o))),t===1))throw n=ki,Rn(e,0),Wt(e,r),ze(e,de()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(T(345));case 2:Sn(e,Ve,Ct);break;case 3:if(Wt(e,r),(r&130023424)===r&&(t=Ou+500-de(),10<t)){if(Ao(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){_e(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=il(Sn.bind(null,e,Ve,Ct),t);break}Sn(e,Ve,Ct);break;case 4:if(Wt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-pt(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=de()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*jx(r/1960))-r,10<r){e.timeoutHandle=il(Sn.bind(null,e,Ve,Ct),r);break}Sn(e,Ve,Ct);break;case 5:Sn(e,Ve,Ct);break;default:throw Error(T(329))}}}return ze(e,de()),e.callbackNode===n?cm.bind(null,e):null}function Pl(e,t){var n=ti;return e.current.memoizedState.isDehydrated&&(Rn(e,t).flags|=256),e=Xo(e,t),e!==2&&(t=Ve,Ve=n,t!==null&&bl(t)),e}function bl(e){Ve===null?Ve=e:Ve.push.apply(Ve,e)}function Rx(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!mt(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Wt(e,t){for(t&=~Iu,t&=~Ss,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-pt(t),r=1<<n;e[n]=-1,t&=~r}}function Ad(e){if(W&6)throw Error(T(327));hr();var t=Ao(e,0);if(!(t&1))return ze(e,de()),null;var n=Xo(e,t);if(e.tag!==0&&n===2){var r=Xa(e);r!==0&&(t=r,n=Pl(e,r))}if(n===1)throw n=ki,Rn(e,0),Wt(e,t),ze(e,de()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Sn(e,Ve,Ct),ze(e,de()),null}function Fu(e,t){var n=W;W|=1;try{return e(t)}finally{W=n,W===0&&(Sr=de()+500,vs&&pn())}}function Vn(e){Kt!==null&&Kt.tag===0&&!(W&6)&&hr();var t=W;W|=1;var n=it.transition,r=K;try{if(it.transition=null,K=1,e)return e()}finally{K=r,it.transition=n,W=t,!(W&6)&&pn()}}function zu(){We=ir.current,ee(ir)}function Rn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,sx(n)),me!==null)for(n=me.return;n!==null;){var r=n;switch(wu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Io();break;case 3:xr(),ee(Oe),ee(Ne),ju();break;case 5:Nu(r);break;case 4:xr();break;case 13:ee(ne);break;case 19:ee(ne);break;case 10:Pu(r.type._context);break;case 22:case 23:zu()}n=n.return}if(we=e,me=e=tn(e.current,null),Ce=We=t,ve=0,ki=null,Iu=Ss=Dn=0,Ve=ti=null,En!==null){for(t=0;t<En.length;t++)if(n=En[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}En=null}return e}function dm(e,t){do{var n=me;try{if(ku(),wo.current=Ko,Ho){for(var r=oe.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Ho=!1}if(Ln=0,xe=ge=oe=null,Jr=!1,wi=0,Vu.current=null,n===null||n.return===null){ve=1,ki=t,me=null;break}e:{var o=e,s=n.return,a=n,l=t;if(t=Ce,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,c=d.tag;if(!(d.mode&1)&&(c===0||c===11||c===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var g=xd(s);if(g!==null){g.flags&=-257,wd(g,s,a,o,t),g.mode&1&&yd(o,u,t),t=g,l=u;var w=t.updateQueue;if(w===null){var x=new Set;x.add(l),t.updateQueue=x}else w.add(l);break e}else{if(!(t&1)){yd(o,u,t),Bu();break e}l=Error(T(426))}}else if(te&&a.mode&1){var C=xd(s);if(C!==null){!(C.flags&65536)&&(C.flags|=256),wd(C,s,a,o,t),Su(wr(l,a));break e}}o=l=wr(l,a),ve!==4&&(ve=2),ti===null?ti=[o]:ti.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var v=Qh(o,l,t);fd(o,v);break e;case 1:a=l;var p=o.type,m=o.stateNode;if(!(o.flags&128)&&(typeof p.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Jt===null||!Jt.has(m)))){o.flags|=65536,t&=-t,o.lanes|=t;var S=Yh(o,a,t);fd(o,S);break e}}o=o.return}while(o!==null)}hm(n)}catch(k){t=k,me===n&&n!==null&&(me=n=n.return);continue}break}while(1)}function fm(){var e=Go.current;return Go.current=Ko,e===null?Ko:e}function Bu(){(ve===0||ve===3||ve===2)&&(ve=4),we===null||!(Dn&268435455)&&!(Ss&268435455)||Wt(we,Ce)}function Xo(e,t){var n=W;W|=2;var r=fm();(we!==e||Ce!==t)&&(Ct=null,Rn(e,t));do try{Mx();break}catch(i){dm(e,i)}while(1);if(ku(),W=n,Go.current=r,me!==null)throw Error(T(261));return we=null,Ce=0,ve}function Mx(){for(;me!==null;)pm(me)}function Ax(){for(;me!==null&&!r0();)pm(me)}function pm(e){var t=gm(e.alternate,e,We);e.memoizedProps=e.pendingProps,t===null?hm(e):me=t,Vu.current=null}function hm(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=bx(n,t),n!==null){n.flags&=32767,me=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ve=6,me=null;return}}else if(n=Px(n,t,We),n!==null){me=n;return}if(t=t.sibling,t!==null){me=t;return}me=t=e}while(t!==null);ve===0&&(ve=5)}function Sn(e,t,n){var r=K,i=it.transition;try{it.transition=null,K=1,_x(e,t,n,r)}finally{it.transition=i,K=r}return null}function _x(e,t,n,r){do hr();while(Kt!==null);if(W&6)throw Error(T(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(p0(e,o),e===we&&(me=we=null,Ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||io||(io=!0,vm(Mo,function(){return hr(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=it.transition,it.transition=null;var s=K;K=1;var a=W;W|=4,Vu.current=null,Tx(e,n),lm(n,e),J0(nl),_o=!!tl,nl=tl=null,e.current=n,Nx(n),i0(),W=a,K=s,it.transition=o}else e.current=n;if(io&&(io=!1,Kt=e,Yo=i),o=e.pendingLanes,o===0&&(Jt=null),a0(n.stateNode),ze(e,de()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Qo)throw Qo=!1,e=Cl,Cl=null,e;return Yo&1&&e.tag!==0&&hr(),o=e.pendingLanes,o&1?e===kl?ni++:(ni=0,kl=e):ni=0,pn(),null}function hr(){if(Kt!==null){var e=Qp(Yo),t=it.transition,n=K;try{if(it.transition=null,K=16>e?16:e,Kt===null)var r=!1;else{if(e=Kt,Kt=null,Yo=0,W&6)throw Error(T(331));var i=W;for(W|=4,M=e.current;M!==null;){var o=M,s=o.child;if(M.flags&16){var a=o.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(M=u;M!==null;){var d=M;switch(d.tag){case 0:case 11:case 15:ei(8,d,o)}var c=d.child;if(c!==null)c.return=d,M=c;else for(;M!==null;){d=M;var f=d.sibling,g=d.return;if(om(d),d===u){M=null;break}if(f!==null){f.return=g,M=f;break}M=g}}}var w=o.alternate;if(w!==null){var x=w.child;if(x!==null){w.child=null;do{var C=x.sibling;x.sibling=null,x=C}while(x!==null)}}M=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,M=s;else e:for(;M!==null;){if(o=M,o.flags&2048)switch(o.tag){case 0:case 11:case 15:ei(9,o,o.return)}var v=o.sibling;if(v!==null){v.return=o.return,M=v;break e}M=o.return}}var p=e.current;for(M=p;M!==null;){s=M;var m=s.child;if(s.subtreeFlags&2064&&m!==null)m.return=s,M=m;else e:for(s=p;M!==null;){if(a=M,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:ws(9,a)}}catch(k){ae(a,a.return,k)}if(a===s){M=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,M=S;break e}M=a.return}}if(W=i,pn(),xt&&typeof xt.onPostCommitFiberRoot=="function")try{xt.onPostCommitFiberRoot(fs,e)}catch{}r=!0}return r}finally{K=n,it.transition=t}}return!1}function _d(e,t,n){t=wr(n,t),t=Qh(e,t,1),e=Zt(e,t,1),t=_e(),e!==null&&(Mi(e,1,t),ze(e,t))}function ae(e,t,n){if(e.tag===3)_d(e,e,n);else for(;t!==null;){if(t.tag===3){_d(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Jt===null||!Jt.has(r))){e=wr(n,e),e=Yh(t,e,1),t=Zt(t,e,1),e=_e(),t!==null&&(Mi(t,1,e),ze(t,e));break}}t=t.return}}function Lx(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=_e(),e.pingedLanes|=e.suspendedLanes&n,we===e&&(Ce&n)===n&&(ve===4||ve===3&&(Ce&130023424)===Ce&&500>de()-Ou?Rn(e,0):Iu|=n),ze(e,t)}function mm(e,t){t===0&&(e.mode&1?(t=Qi,Qi<<=1,!(Qi&130023424)&&(Qi=4194304)):t=1);var n=_e();e=At(e,t),e!==null&&(Mi(e,t,n),ze(e,n))}function Dx(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),mm(e,n)}function Vx(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(T(314))}r!==null&&r.delete(t),mm(e,n)}var gm;gm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Oe.current)Ie=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ie=!1,kx(e,t,n);Ie=!!(e.flags&131072)}else Ie=!1,te&&t.flags&1048576&&wh(t,zo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Co(e,t),e=t.pendingProps;var i=gr(t,Ne.current);pr(t,n),i=Mu(null,t,r,e,i,n);var o=Au();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Fe(r)?(o=!0,Oo(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Eu(t),i.updater=xs,t.stateNode=i,i._reactInternals=t,dl(t,r,e,n),t=hl(null,t,r,!0,o,n)):(t.tag=0,te&&o&&xu(t),Ae(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Co(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=Ox(r),e=ct(r,e),i){case 0:t=pl(null,t,r,e,n);break e;case 1:t=kd(null,t,r,e,n);break e;case 11:t=Sd(null,t,r,e,n);break e;case 14:t=Cd(null,t,r,ct(r.type,e),n);break e}throw Error(T(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ct(r,i),pl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ct(r,i),kd(e,t,r,i,n);case 3:e:{if(Jh(t),e===null)throw Error(T(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Eh(e,t),$o(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=wr(Error(T(423)),t),t=Pd(e,t,r,n,i);break e}else if(r!==i){i=wr(Error(T(424)),t),t=Pd(e,t,r,n,i);break e}else for(He=qt(t.stateNode.containerInfo.firstChild),Ke=t,te=!0,ft=null,n=Ph(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(vr(),r===i){t=_t(e,t,n);break e}Ae(e,t,r,n)}t=t.child}return t;case 5:return Th(t),e===null&&ll(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,rl(r,i)?s=null:o!==null&&rl(r,o)&&(t.flags|=32),Zh(e,t),Ae(e,t,s,n),t.child;case 6:return e===null&&ll(t),null;case 13:return em(e,t,n);case 4:return Tu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=yr(t,null,r,n):Ae(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ct(r,i),Sd(e,t,r,i,n);case 7:return Ae(e,t,t.pendingProps,n),t.child;case 8:return Ae(e,t,t.pendingProps.children,n),t.child;case 12:return Ae(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,X(Bo,r._currentValue),r._currentValue=s,o!==null)if(mt(o.value,s)){if(o.children===i.children&&!Oe.current){t=_t(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){s=o.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=Tt(-1,n&-n),l.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),ul(o.return,n,t),a.lanes|=n;break}l=l.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(T(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),ul(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}Ae(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,pr(t,n),i=ot(i),r=r(i),t.flags|=1,Ae(e,t,r,n),t.child;case 14:return r=t.type,i=ct(r,t.pendingProps),i=ct(r.type,i),Cd(e,t,r,i,n);case 15:return Xh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ct(r,i),Co(e,t),t.tag=1,Fe(r)?(e=!0,Oo(t)):e=!1,pr(t,n),Gh(t,r,i),dl(t,r,i,n),hl(null,t,r,!0,e,n);case 19:return tm(e,t,n);case 22:return qh(e,t,n)}throw Error(T(156,t.tag))};function vm(e,t){return Wp(e,t)}function Ix(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function rt(e,t,n,r){return new Ix(e,t,n,r)}function Uu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ox(e){if(typeof e=="function")return Uu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===au)return 11;if(e===lu)return 14}return 2}function tn(e,t){var n=e.alternate;return n===null?(n=rt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function bo(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")Uu(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Qn:return Mn(n.children,i,o,t);case su:s=8,i|=8;break;case Da:return e=rt(12,n,t,i|2),e.elementType=Da,e.lanes=o,e;case Va:return e=rt(13,n,t,i),e.elementType=Va,e.lanes=o,e;case Ia:return e=rt(19,n,t,i),e.elementType=Ia,e.lanes=o,e;case Tp:return Cs(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case bp:s=10;break e;case Ep:s=9;break e;case au:s=11;break e;case lu:s=14;break e;case zt:s=16,r=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=rt(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function Mn(e,t,n,r){return e=rt(7,e,r,t),e.lanes=n,e}function Cs(e,t,n,r){return e=rt(22,e,r,t),e.elementType=Tp,e.lanes=n,e.stateNode={isHidden:!1},e}function ha(e,t,n){return e=rt(6,e,null,t),e.lanes=n,e}function ma(e,t,n){return t=rt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Fx(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ys(0),this.expirationTimes=Ys(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ys(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function $u(e,t,n,r,i,o,s,a,l){return e=new Fx(e,t,n,a,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=rt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Eu(o),e}function zx(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Gn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function ym(e){if(!e)return ln;e=e._reactInternals;e:{if(Fn(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Fe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(Fe(n))return yh(e,n,t)}return t}function xm(e,t,n,r,i,o,s,a,l){return e=$u(n,r,!0,e,i,o,s,a,l),e.context=ym(null),n=e.current,r=_e(),i=en(n),o=Tt(r,i),o.callback=t??null,Zt(n,o,i),e.current.lanes=i,Mi(e,i,r),ze(e,r),e}function ks(e,t,n,r){var i=t.current,o=_e(),s=en(i);return n=ym(n),t.context===null?t.context=n:t.pendingContext=n,t=Tt(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Zt(i,t,s),e!==null&&(ht(e,i,s,o),xo(e,i,s)),s}function qo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ld(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Wu(e,t){Ld(e,t),(e=e.alternate)&&Ld(e,t)}function Bx(){return null}var wm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Hu(e){this._internalRoot=e}Ps.prototype.render=Hu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));ks(e,t,null,null)};Ps.prototype.unmount=Hu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Vn(function(){ks(null,e,null,null)}),t[Mt]=null}};function Ps(e){this._internalRoot=e}Ps.prototype.unstable_scheduleHydration=function(e){if(e){var t=qp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<$t.length&&t!==0&&t<$t[n].priority;n++);$t.splice(n,0,e),n===0&&Jp(e)}};function Ku(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function bs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Dd(){}function Ux(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=qo(s);o.call(u)}}var s=xm(t,r,e,0,null,!1,!1,"",Dd);return e._reactRootContainer=s,e[Mt]=s.current,mi(e.nodeType===8?e.parentNode:e),Vn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=qo(l);a.call(u)}}var l=$u(e,0,!1,null,null,!1,!1,"",Dd);return e._reactRootContainer=l,e[Mt]=l.current,mi(e.nodeType===8?e.parentNode:e),Vn(function(){ks(t,l,n,r)}),l}function Es(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var a=i;i=function(){var l=qo(s);a.call(l)}}ks(t,s,e,i)}else s=Ux(n,t,e,i,r);return qo(s)}Yp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Hr(t.pendingLanes);n!==0&&(du(t,n|1),ze(t,de()),!(W&6)&&(Sr=de()+500,pn()))}break;case 13:Vn(function(){var r=At(e,1);if(r!==null){var i=_e();ht(r,e,1,i)}}),Wu(e,1)}};fu=function(e){if(e.tag===13){var t=At(e,134217728);if(t!==null){var n=_e();ht(t,e,134217728,n)}Wu(e,134217728)}};Xp=function(e){if(e.tag===13){var t=en(e),n=At(e,t);if(n!==null){var r=_e();ht(n,e,t,r)}Wu(e,t)}};qp=function(){return K};Zp=function(e,t){var n=K;try{return K=e,t()}finally{K=n}};Ga=function(e,t,n){switch(t){case"input":if(za(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=gs(r);if(!i)throw Error(T(90));jp(r),za(r,i)}}}break;case"textarea":Mp(e,n);break;case"select":t=n.value,t!=null&&ur(e,!!n.multiple,t,!1)}};Op=Fu;Fp=Vn;var $x={usingClientEntryPoint:!1,Events:[_i,Zn,gs,Vp,Ip,Fu]},Or={findFiberByHostInstance:bn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Wx={bundleType:Or.bundleType,version:Or.version,rendererPackageName:Or.rendererPackageName,rendererConfig:Or.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Dt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Up(e),e===null?null:e.stateNode},findFiberByHostInstance:Or.findFiberByHostInstance||Bx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var oo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!oo.isDisabled&&oo.supportsFiber)try{fs=oo.inject(Wx),xt=oo}catch{}}Xe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=$x;Xe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ku(t))throw Error(T(200));return zx(e,t,null,n)};Xe.createRoot=function(e,t){if(!Ku(e))throw Error(T(299));var n=!1,r="",i=wm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=$u(e,1,!1,null,null,n,!1,r,i),e[Mt]=t.current,mi(e.nodeType===8?e.parentNode:e),new Hu(t)};Xe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=Up(t),e=e===null?null:e.stateNode,e};Xe.flushSync=function(e){return Vn(e)};Xe.hydrate=function(e,t,n){if(!bs(t))throw Error(T(200));return Es(null,e,t,!0,n)};Xe.hydrateRoot=function(e,t,n){if(!Ku(e))throw Error(T(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=wm;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=xm(t,null,e,1,n??null,i,!1,o,s),e[Mt]=t.current,mi(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Ps(t)};Xe.render=function(e,t,n){if(!bs(t))throw Error(T(200));return Es(null,e,t,!1,n)};Xe.unmountComponentAtNode=function(e){if(!bs(e))throw Error(T(40));return e._reactRootContainer?(Vn(function(){Es(null,null,e,!1,function(){e._reactRootContainer=null,e[Mt]=null})}),!0):!1};Xe.unstable_batchedUpdates=Fu;Xe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!bs(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return Es(e,t,n,!1,r)};Xe.version="18.3.1-next-f1338f8080-20240426";function Sm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Sm)}catch(e){console.error(e)}}Sm(),Sp.exports=Xe;var Ts=Sp.exports;const Hx=lp(Ts);var Vd=Ts;_a.createRoot=Vd.createRoot,_a.hydrateRoot=Vd.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Pi(){return Pi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pi.apply(this,arguments)}var Gt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Gt||(Gt={}));const Id="popstate";function Kx(e){e===void 0&&(e={});function t(r,i){let{pathname:o,search:s,hash:a}=r.location;return El("",{pathname:o,search:s,hash:a},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:Zo(i)}return Qx(t,n,null,e)}function ue(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Cm(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Gx(){return Math.random().toString(36).substr(2,8)}function Od(e,t){return{usr:e.state,key:e.key,idx:t}}function El(e,t,n,r){return n===void 0&&(n=null),Pi({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?jr(t):t,{state:n,key:t&&t.key||r||Gx()})}function Zo(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function jr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Qx(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:o=!1}=r,s=i.history,a=Gt.Pop,l=null,u=d();u==null&&(u=0,s.replaceState(Pi({},s.state,{idx:u}),""));function d(){return(s.state||{idx:null}).idx}function c(){a=Gt.Pop;let C=d(),v=C==null?null:C-u;u=C,l&&l({action:a,location:x.location,delta:v})}function f(C,v){a=Gt.Push;let p=El(x.location,C,v);n&&n(p,C),u=d()+1;let m=Od(p,u),S=x.createHref(p);try{s.pushState(m,"",S)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;i.location.assign(S)}o&&l&&l({action:a,location:x.location,delta:1})}function g(C,v){a=Gt.Replace;let p=El(x.location,C,v);n&&n(p,C),u=d();let m=Od(p,u),S=x.createHref(p);s.replaceState(m,"",S),o&&l&&l({action:a,location:x.location,delta:0})}function w(C){let v=i.location.origin!=="null"?i.location.origin:i.location.href,p=typeof C=="string"?C:Zo(C);return p=p.replace(/ $/,"%20"),ue(v,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,v)}let x={get action(){return a},get location(){return e(i,s)},listen(C){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(Id,c),l=C,()=>{i.removeEventListener(Id,c),l=null}},createHref(C){return t(i,C)},createURL:w,encodeLocation(C){let v=w(C);return{pathname:v.pathname,search:v.search,hash:v.hash}},push:f,replace:g,go(C){return s.go(C)}};return x}var Fd;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Fd||(Fd={}));function Yx(e,t,n){return n===void 0&&(n="/"),Xx(e,t,n,!1)}function Xx(e,t,n,r){let i=typeof t=="string"?jr(t):t,o=Cr(i.pathname||"/",n);if(o==null)return null;let s=km(e);qx(s);let a=null;for(let l=0;a==null&&l<s.length;++l){let u=l1(o);a=s1(s[l],u,r)}return a}function km(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(o,s,a)=>{let l={relativePath:a===void 0?o.path||"":a,caseSensitive:o.caseSensitive===!0,childrenIndex:s,route:o};l.relativePath.startsWith("/")&&(ue(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=nn([r,l.relativePath]),d=n.concat(l);o.children&&o.children.length>0&&(ue(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),km(o.children,t,d,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:i1(u,o.index),routesMeta:d})};return e.forEach((o,s)=>{var a;if(o.path===""||!((a=o.path)!=null&&a.includes("?")))i(o,s);else for(let l of Pm(o.path))i(o,s,l)}),t}function Pm(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return i?[o,""]:[o];let s=Pm(r.join("/")),a=[];return a.push(...s.map(l=>l===""?o:[o,l].join("/"))),i&&a.push(...s),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function qx(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:o1(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Zx=/^:[\w-]+$/,Jx=3,e1=2,t1=1,n1=10,r1=-2,zd=e=>e==="*";function i1(e,t){let n=e.split("/"),r=n.length;return n.some(zd)&&(r+=r1),t&&(r+=e1),n.filter(i=>!zd(i)).reduce((i,o)=>i+(Zx.test(o)?Jx:o===""?t1:n1),r)}function o1(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function s1(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,i={},o="/",s=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,d=o==="/"?t:t.slice(o.length)||"/",c=Jo({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},d),f=l.route;if(!c&&u&&n&&!r[r.length-1].route.index&&(c=Jo({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},d)),!c)return null;Object.assign(i,c.params),s.push({params:i,pathname:nn([o,c.pathname]),pathnameBase:f1(nn([o,c.pathnameBase])),route:f}),c.pathnameBase!=="/"&&(o=nn([o,c.pathnameBase]))}return s}function Jo(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=a1(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let o=i[0],s=o.replace(/(.)\/+$/,"$1"),a=i.slice(1);return{params:r.reduce((u,d,c)=>{let{paramName:f,isOptional:g}=d;if(f==="*"){let x=a[c]||"";s=o.slice(0,o.length-x.length).replace(/(.)\/+$/,"$1")}const w=a[c];return g&&!w?u[f]=void 0:u[f]=(w||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:s,pattern:e}}function a1(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Cm(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function l1(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Cm(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Cr(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function u1(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?jr(e):e;return{pathname:n?n.startsWith("/")?n:c1(n,t):t,search:p1(r),hash:h1(i)}}function c1(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function ga(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function d1(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function bm(e,t){let n=d1(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Em(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=jr(e):(i=Pi({},e),ue(!i.pathname||!i.pathname.includes("?"),ga("?","pathname","search",i)),ue(!i.pathname||!i.pathname.includes("#"),ga("#","pathname","hash",i)),ue(!i.search||!i.search.includes("#"),ga("#","search","hash",i)));let o=e===""||i.pathname==="",s=o?"/":i.pathname,a;if(s==null)a=n;else{let c=t.length-1;if(!r&&s.startsWith("..")){let f=s.split("/");for(;f[0]==="..";)f.shift(),c-=1;i.pathname=f.join("/")}a=c>=0?t[c]:"/"}let l=u1(i,a),u=s&&s!=="/"&&s.endsWith("/"),d=(o||s===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||d)&&(l.pathname+="/"),l}const nn=e=>e.join("/").replace(/\/\/+/g,"/"),f1=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),p1=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,h1=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function m1(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Tm=["post","put","patch","delete"];new Set(Tm);const g1=["get",...Tm];new Set(g1);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function bi(){return bi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},bi.apply(this,arguments)}const Ns=y.createContext(null),Nm=y.createContext(null),hn=y.createContext(null),js=y.createContext(null),mn=y.createContext({outlet:null,matches:[],isDataRoute:!1}),jm=y.createContext(null);function v1(e,t){let{relative:n}=t===void 0?{}:t;Di()||ue(!1);let{basename:r,navigator:i}=y.useContext(hn),{hash:o,pathname:s,search:a}=Rs(e,{relative:n}),l=s;return r!=="/"&&(l=s==="/"?r:nn([r,s])),i.createHref({pathname:l,search:a,hash:o})}function Di(){return y.useContext(js)!=null}function zn(){return Di()||ue(!1),y.useContext(js).location}function Rm(e){y.useContext(hn).static||y.useLayoutEffect(e)}function y1(){let{isDataRoute:e}=y.useContext(mn);return e?A1():x1()}function x1(){Di()||ue(!1);let e=y.useContext(Ns),{basename:t,future:n,navigator:r}=y.useContext(hn),{matches:i}=y.useContext(mn),{pathname:o}=zn(),s=JSON.stringify(bm(i,n.v7_relativeSplatPath)),a=y.useRef(!1);return Rm(()=>{a.current=!0}),y.useCallback(function(u,d){if(d===void 0&&(d={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let c=Em(u,JSON.parse(s),o,d.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:nn([t,c.pathname])),(d.replace?r.replace:r.push)(c,d.state,d)},[t,r,s,o,e])}function w1(){let{matches:e}=y.useContext(mn),t=e[e.length-1];return t?t.params:{}}function Rs(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=y.useContext(hn),{matches:i}=y.useContext(mn),{pathname:o}=zn(),s=JSON.stringify(bm(i,r.v7_relativeSplatPath));return y.useMemo(()=>Em(e,JSON.parse(s),o,n==="path"),[e,s,o,n])}function S1(e,t){return C1(e,t)}function C1(e,t,n,r){Di()||ue(!1);let{navigator:i}=y.useContext(hn),{matches:o}=y.useContext(mn),s=o[o.length-1],a=s?s.params:{};s&&s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let u=zn(),d;if(t){var c;let C=typeof t=="string"?jr(t):t;l==="/"||(c=C.pathname)!=null&&c.startsWith(l)||ue(!1),d=C}else d=u;let f=d.pathname||"/",g=f;if(l!=="/"){let C=l.replace(/^\//,"").split("/");g="/"+f.replace(/^\//,"").split("/").slice(C.length).join("/")}let w=Yx(e,{pathname:g}),x=T1(w&&w.map(C=>Object.assign({},C,{params:Object.assign({},a,C.params),pathname:nn([l,i.encodeLocation?i.encodeLocation(C.pathname).pathname:C.pathname]),pathnameBase:C.pathnameBase==="/"?l:nn([l,i.encodeLocation?i.encodeLocation(C.pathnameBase).pathname:C.pathnameBase])})),o,n,r);return t&&x?y.createElement(js.Provider,{value:{location:bi({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Gt.Pop}},x):x}function k1(){let e=M1(),t=m1(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},o=null;return y.createElement(y.Fragment,null,y.createElement("h2",null,"Unexpected Application Error!"),y.createElement("h3",{style:{fontStyle:"italic"}},t),n?y.createElement("pre",{style:i},n):null,o)}const P1=y.createElement(k1,null);class b1 extends y.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?y.createElement(mn.Provider,{value:this.props.routeContext},y.createElement(jm.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function E1(e){let{routeContext:t,match:n,children:r}=e,i=y.useContext(Ns);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),y.createElement(mn.Provider,{value:t},r)}function T1(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,a=(i=n)==null?void 0:i.errors;if(a!=null){let d=s.findIndex(c=>c.route.id&&(a==null?void 0:a[c.route.id])!==void 0);d>=0||ue(!1),s=s.slice(0,Math.min(s.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<s.length;d++){let c=s[d];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(u=d),c.route.id){let{loaderData:f,errors:g}=n,w=c.route.loader&&f[c.route.id]===void 0&&(!g||g[c.route.id]===void 0);if(c.route.lazy||w){l=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((d,c,f)=>{let g,w=!1,x=null,C=null;n&&(g=a&&c.route.id?a[c.route.id]:void 0,x=c.route.errorElement||P1,l&&(u<0&&f===0?(_1("route-fallback",!1),w=!0,C=null):u===f&&(w=!0,C=c.route.hydrateFallbackElement||null)));let v=t.concat(s.slice(0,f+1)),p=()=>{let m;return g?m=x:w?m=C:c.route.Component?m=y.createElement(c.route.Component,null):c.route.element?m=c.route.element:m=d,y.createElement(E1,{match:c,routeContext:{outlet:d,matches:v,isDataRoute:n!=null},children:m})};return n&&(c.route.ErrorBoundary||c.route.errorElement||f===0)?y.createElement(b1,{location:n.location,revalidation:n.revalidation,component:x,error:g,children:p(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):p()},null)}var Mm=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Mm||{}),es=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(es||{});function N1(e){let t=y.useContext(Ns);return t||ue(!1),t}function j1(e){let t=y.useContext(Nm);return t||ue(!1),t}function R1(e){let t=y.useContext(mn);return t||ue(!1),t}function Am(e){let t=R1(),n=t.matches[t.matches.length-1];return n.route.id||ue(!1),n.route.id}function M1(){var e;let t=y.useContext(jm),n=j1(es.UseRouteError),r=Am(es.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function A1(){let{router:e}=N1(Mm.UseNavigateStable),t=Am(es.UseNavigateStable),n=y.useRef(!1);return Rm(()=>{n.current=!0}),y.useCallback(function(i,o){o===void 0&&(o={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,bi({fromRouteId:t},o)))},[e,t])}const Bd={};function _1(e,t,n){!t&&!Bd[e]&&(Bd[e]=!0)}function L1(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Kn(e){ue(!1)}function D1(e){let{basename:t="/",children:n=null,location:r,navigationType:i=Gt.Pop,navigator:o,static:s=!1,future:a}=e;Di()&&ue(!1);let l=t.replace(/^\/*/,"/"),u=y.useMemo(()=>({basename:l,navigator:o,static:s,future:bi({v7_relativeSplatPath:!1},a)}),[l,a,o,s]);typeof r=="string"&&(r=jr(r));let{pathname:d="/",search:c="",hash:f="",state:g=null,key:w="default"}=r,x=y.useMemo(()=>{let C=Cr(d,l);return C==null?null:{location:{pathname:C,search:c,hash:f,state:g,key:w},navigationType:i}},[l,d,c,f,g,w,i]);return x==null?null:y.createElement(hn.Provider,{value:u},y.createElement(js.Provider,{children:n,value:x}))}function V1(e){let{children:t,location:n}=e;return S1(Tl(t),n)}new Promise(()=>{});function Tl(e,t){t===void 0&&(t=[]);let n=[];return y.Children.forEach(e,(r,i)=>{if(!y.isValidElement(r))return;let o=[...t,i];if(r.type===y.Fragment){n.push.apply(n,Tl(r.props.children,o));return}r.type!==Kn&&ue(!1),!r.props.index||!r.props.children||ue(!1);let s={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Tl(r.props.children,o)),n.push(s)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ts(){return ts=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ts.apply(this,arguments)}function _m(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function I1(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function O1(e,t){return e.button===0&&(!t||t==="_self")&&!I1(e)}const F1=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],z1=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],B1="6";try{window.__reactRouterVersion=B1}catch{}const U1=y.createContext({isTransitioning:!1}),$1="startTransition",Ud=xp[$1];function W1(e){let{basename:t,children:n,future:r,window:i}=e,o=y.useRef();o.current==null&&(o.current=Kx({window:i,v5Compat:!0}));let s=o.current,[a,l]=y.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},d=y.useCallback(c=>{u&&Ud?Ud(()=>l(c)):l(c)},[l,u]);return y.useLayoutEffect(()=>s.listen(d),[s,d]),y.useEffect(()=>L1(r),[r]),y.createElement(D1,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:s,future:r})}const H1=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",K1=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,nt=y.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:o,replace:s,state:a,target:l,to:u,preventScrollReset:d,viewTransition:c}=t,f=_m(t,F1),{basename:g}=y.useContext(hn),w,x=!1;if(typeof u=="string"&&K1.test(u)&&(w=u,H1))try{let m=new URL(window.location.href),S=u.startsWith("//")?new URL(m.protocol+u):new URL(u),k=Cr(S.pathname,g);S.origin===m.origin&&k!=null?u=k+S.search+S.hash:x=!0}catch{}let C=v1(u,{relative:i}),v=Q1(u,{replace:s,state:a,target:l,preventScrollReset:d,relative:i,viewTransition:c});function p(m){r&&r(m),m.defaultPrevented||v(m)}return y.createElement("a",ts({},f,{href:w||C,onClick:x||o?r:p,ref:n,target:l}))}),$d=y.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:i=!1,className:o="",end:s=!1,style:a,to:l,viewTransition:u,children:d}=t,c=_m(t,z1),f=Rs(l,{relative:c.relative}),g=zn(),w=y.useContext(Nm),{navigator:x,basename:C}=y.useContext(hn),v=w!=null&&Y1(f)&&u===!0,p=x.encodeLocation?x.encodeLocation(f).pathname:f.pathname,m=g.pathname,S=w&&w.navigation&&w.navigation.location?w.navigation.location.pathname:null;i||(m=m.toLowerCase(),S=S?S.toLowerCase():null,p=p.toLowerCase()),S&&C&&(S=Cr(S,C)||S);const k=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let b=m===p||!s&&m.startsWith(p)&&m.charAt(k)==="/",P=S!=null&&(S===p||!s&&S.startsWith(p)&&S.charAt(p.length)==="/"),E={isActive:b,isPending:P,isTransitioning:v},R=b?r:void 0,j;typeof o=="function"?j=o(E):j=[o,b?"active":null,P?"pending":null,v?"transitioning":null].filter(Boolean).join(" ");let O=typeof a=="function"?a(E):a;return y.createElement(nt,ts({},c,{"aria-current":R,className:j,ref:n,style:O,to:l,viewTransition:u}),typeof d=="function"?d(E):d)});var Nl;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Nl||(Nl={}));var Wd;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Wd||(Wd={}));function G1(e){let t=y.useContext(Ns);return t||ue(!1),t}function Q1(e,t){let{target:n,replace:r,state:i,preventScrollReset:o,relative:s,viewTransition:a}=t===void 0?{}:t,l=y1(),u=zn(),d=Rs(e,{relative:s});return y.useCallback(c=>{if(O1(c,n)){c.preventDefault();let f=r!==void 0?r:Zo(u)===Zo(d);l(e,{replace:f,state:i,preventScrollReset:o,relative:s,viewTransition:a})}},[u,l,d,r,i,n,e,o,s,a])}function Y1(e,t){t===void 0&&(t={});let n=y.useContext(U1);n==null&&ue(!1);let{basename:r}=G1(Nl.useViewTransitionState),i=Rs(e,{relative:t.relative});if(!n.isTransitioning)return!1;let o=Cr(n.currentLocation.pathname,r)||n.currentLocation.pathname,s=Cr(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Jo(i.pathname,s)!=null||Jo(i.pathname,o)!=null}const Lm=y.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Ms=y.createContext({}),As=y.createContext(null),_s=typeof document<"u",Gu=_s?y.useLayoutEffect:y.useEffect,Dm=y.createContext({strict:!1}),Qu=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),X1="framerAppearId",Vm="data-"+Qu(X1);function q1(e,t,n,r){const{visualElement:i}=y.useContext(Ms),o=y.useContext(Dm),s=y.useContext(As),a=y.useContext(Lm).reducedMotion,l=y.useRef();r=r||o.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:a}));const u=l.current;y.useInsertionEffect(()=>{u&&u.update(n,s)});const d=y.useRef(!!(n[Vm]&&!window.HandoffComplete));return Gu(()=>{u&&(u.render(),d.current&&u.animationState&&u.animationState.animateChanges())}),y.useEffect(()=>{u&&(u.updateFeatures(),!d.current&&u.animationState&&u.animationState.animateChanges(),d.current&&(d.current=!1,window.HandoffComplete=!0))}),u}function or(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Z1(e,t,n){return y.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):or(n)&&(n.current=r))},[t])}function Ei(e){return typeof e=="string"||Array.isArray(e)}function Ls(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Yu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Xu=["initial",...Yu];function Ds(e){return Ls(e.animate)||Xu.some(t=>Ei(e[t]))}function Im(e){return!!(Ds(e)||e.variants)}function J1(e,t){if(Ds(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Ei(n)?n:void 0,animate:Ei(r)?r:void 0}}return e.inherit!==!1?t:{}}function ew(e){const{initial:t,animate:n}=J1(e,y.useContext(Ms));return y.useMemo(()=>({initial:t,animate:n}),[Hd(t),Hd(n)])}function Hd(e){return Array.isArray(e)?e.join(" "):e}const Kd={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Ti={};for(const e in Kd)Ti[e]={isEnabled:t=>Kd[e].some(n=>!!t[n])};function tw(e){for(const t in e)Ti[t]={...Ti[t],...e[t]}}const qu=y.createContext({}),Om=y.createContext({}),nw=Symbol.for("motionComponentSymbol");function rw({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&tw(e);function o(a,l){let u;const d={...y.useContext(Lm),...a,layoutId:iw(a)},{isStatic:c}=d,f=ew(a),g=r(a,c);if(!c&&_s){f.visualElement=q1(i,g,d,t);const w=y.useContext(Om),x=y.useContext(Dm).strict;f.visualElement&&(u=f.visualElement.loadFeatures(d,x,e,w))}return y.createElement(Ms.Provider,{value:f},u&&f.visualElement?y.createElement(u,{visualElement:f.visualElement,...d}):null,n(i,a,Z1(g,f.visualElement,l),g,c,f.visualElement))}const s=y.forwardRef(o);return s[nw]=i,s}function iw({layoutId:e}){const t=y.useContext(qu).id;return t&&e!==void 0?t+"-"+e:e}function ow(e){function t(r,i={}){return rw(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const sw=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Zu(e){return typeof e!="string"||e.includes("-")?!1:!!(sw.indexOf(e)>-1||/[A-Z]/.test(e))}const ns={};function aw(e){Object.assign(ns,e)}const Vi=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Bn=new Set(Vi);function Fm(e,{layout:t,layoutId:n}){return Bn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!ns[e]||e==="opacity")}const Be=e=>!!(e&&e.getVelocity),lw={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},uw=Vi.length;function cw(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let s=0;s<uw;s++){const a=Vi[s];if(e[a]!==void 0){const l=lw[a]||a;o+=`${l}(${e[a]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}const zm=e=>t=>typeof t=="string"&&t.startsWith(e),Bm=zm("--"),jl=zm("var(--"),dw=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,fw=(e,t)=>t&&typeof e=="number"?t.transform(e):e,un=(e,t,n)=>Math.min(Math.max(n,e),t),Un={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},ri={...Un,transform:e=>un(0,1,e)},so={...Un,default:1},ii=e=>Math.round(e*1e5)/1e5,Vs=/(-)?([\d]*\.?[\d])+/g,Um=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,pw=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Ii(e){return typeof e=="string"}const Oi=e=>({test:t=>Ii(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Ot=Oi("deg"),St=Oi("%"),I=Oi("px"),hw=Oi("vh"),mw=Oi("vw"),Gd={...St,parse:e=>St.parse(e)/100,transform:e=>St.transform(e*100)},Qd={...Un,transform:Math.round},$m={borderWidth:I,borderTopWidth:I,borderRightWidth:I,borderBottomWidth:I,borderLeftWidth:I,borderRadius:I,radius:I,borderTopLeftRadius:I,borderTopRightRadius:I,borderBottomRightRadius:I,borderBottomLeftRadius:I,width:I,maxWidth:I,height:I,maxHeight:I,size:I,top:I,right:I,bottom:I,left:I,padding:I,paddingTop:I,paddingRight:I,paddingBottom:I,paddingLeft:I,margin:I,marginTop:I,marginRight:I,marginBottom:I,marginLeft:I,rotate:Ot,rotateX:Ot,rotateY:Ot,rotateZ:Ot,scale:so,scaleX:so,scaleY:so,scaleZ:so,skew:Ot,skewX:Ot,skewY:Ot,distance:I,translateX:I,translateY:I,translateZ:I,x:I,y:I,z:I,perspective:I,transformPerspective:I,opacity:ri,originX:Gd,originY:Gd,originZ:I,zIndex:Qd,fillOpacity:ri,strokeOpacity:ri,numOctaves:Qd};function Ju(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:a}=e;let l=!1,u=!1,d=!0;for(const c in t){const f=t[c];if(Bm(c)){o[c]=f;continue}const g=$m[c],w=fw(f,g);if(Bn.has(c)){if(l=!0,s[c]=w,!d)continue;f!==(g.default||0)&&(d=!1)}else c.startsWith("origin")?(u=!0,a[c]=w):i[c]=w}if(t.transform||(l||r?i.transform=cw(e.transform,n,d,r):i.transform&&(i.transform="none")),u){const{originX:c="50%",originY:f="50%",originZ:g=0}=a;i.transformOrigin=`${c} ${f} ${g}`}}const ec=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Wm(e,t,n){for(const r in t)!Be(t[r])&&!Fm(r,n)&&(e[r]=t[r])}function gw({transformTemplate:e},t,n){return y.useMemo(()=>{const r=ec();return Ju(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function vw(e,t,n){const r=e.style||{},i={};return Wm(i,r,e),Object.assign(i,gw(e,t,n)),e.transformValues?e.transformValues(i):i}function yw(e,t,n){const r={},i=vw(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const xw=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function rs(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||xw.has(e)}let Hm=e=>!rs(e);function ww(e){e&&(Hm=t=>t.startsWith("on")?!rs(t):e(t))}try{ww(require("@emotion/is-prop-valid").default)}catch{}function Sw(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(Hm(i)||n===!0&&rs(i)||!t&&!rs(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Yd(e,t,n){return typeof e=="string"?e:I.transform(t+n*e)}function Cw(e,t,n){const r=Yd(t,e.x,e.width),i=Yd(n,e.y,e.height);return`${r} ${i}`}const kw={offset:"stroke-dashoffset",array:"stroke-dasharray"},Pw={offset:"strokeDashoffset",array:"strokeDasharray"};function bw(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?kw:Pw;e[o.offset]=I.transform(-r);const s=I.transform(t),a=I.transform(n);e[o.array]=`${s} ${a}`}function tc(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},d,c,f){if(Ju(e,u,d,f),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:g,style:w,dimensions:x}=e;g.transform&&(x&&(w.transform=g.transform),delete g.transform),x&&(i!==void 0||o!==void 0||w.transform)&&(w.transformOrigin=Cw(x,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(g.x=t),n!==void 0&&(g.y=n),r!==void 0&&(g.scale=r),s!==void 0&&bw(g,s,a,l,!1)}const Km=()=>({...ec(),attrs:{}}),nc=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Ew(e,t,n,r){const i=y.useMemo(()=>{const o=Km();return tc(o,t,{enableHardwareAcceleration:!1},nc(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Wm(o,e.style,e),i.style={...o,...i.style}}return i}function Tw(e=!1){return(n,r,i,{latestValues:o},s)=>{const l=(Zu(n)?Ew:yw)(r,o,s,n),d={...Sw(r,typeof n=="string",e),...l,ref:i},{children:c}=r,f=y.useMemo(()=>Be(c)?c.get():c,[c]);return y.createElement(n,{...d,children:f})}}function Gm(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const Qm=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Ym(e,t,n,r){Gm(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Qm.has(i)?i:Qu(i),t.attrs[i])}function rc(e,t){const{style:n}=e,r={};for(const i in n)(Be(n[i])||t.style&&Be(t.style[i])||Fm(i,e))&&(r[i]=n[i]);return r}function Xm(e,t){const n=rc(e,t);for(const r in e)if(Be(e[r])||Be(t[r])){const i=Vi.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function ic(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function qm(e){const t=y.useRef(null);return t.current===null&&(t.current=e()),t.current}const is=e=>Array.isArray(e),Nw=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),jw=e=>is(e)?e[e.length-1]||0:e;function Eo(e){const t=Be(e)?e.get():e;return Nw(t)?t.toValue():t}function Rw({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:Mw(r,i,o,e),renderState:t()};return n&&(s.mount=a=>n(r,a,s)),s}const Zm=e=>(t,n)=>{const r=y.useContext(Ms),i=y.useContext(As),o=()=>Rw(e,t,r,i);return n?o():qm(o)};function Mw(e,t,n,r){const i={},o=r(e,{});for(const f in o)i[f]=Eo(o[f]);let{initial:s,animate:a}=e;const l=Ds(e),u=Im(e);t&&u&&!l&&e.inherit!==!1&&(s===void 0&&(s=t.initial),a===void 0&&(a=t.animate));let d=n?n.initial===!1:!1;d=d||s===!1;const c=d?a:s;return c&&typeof c!="boolean"&&!Ls(c)&&(Array.isArray(c)?c:[c]).forEach(g=>{const w=ic(e,g);if(!w)return;const{transitionEnd:x,transition:C,...v}=w;for(const p in v){let m=v[p];if(Array.isArray(m)){const S=d?m.length-1:0;m=m[S]}m!==null&&(i[p]=m)}for(const p in x)i[p]=x[p]}),i}const le=e=>e;class Xd{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function Aw(e){let t=new Xd,n=new Xd,r=0,i=!1,o=!1;const s=new WeakSet,a={schedule:(l,u=!1,d=!1)=>{const c=d&&i,f=c?t:n;return u&&s.add(l),f.add(l)&&c&&i&&(r=t.order.length),l},cancel:l=>{n.remove(l),s.delete(l)},process:l=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const d=t.order[u];d(l),s.has(d)&&(a.schedule(d),e())}i=!1,o&&(o=!1,a.process(l))}};return a}const ao=["prepare","read","update","preRender","render","postRender"],_w=40;function Lw(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=ao.reduce((c,f)=>(c[f]=Aw(()=>n=!0),c),{}),s=c=>o[c].process(i),a=()=>{const c=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(c-i.timestamp,_w),1),i.timestamp=c,i.isProcessing=!0,ao.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(a))},l=()=>{n=!0,r=!0,i.isProcessing||e(a)};return{schedule:ao.reduce((c,f)=>{const g=o[f];return c[f]=(w,x=!1,C=!1)=>(n||l(),g.schedule(w,x,C)),c},{}),cancel:c=>ao.forEach(f=>o[f].cancel(c)),state:i,steps:o}}const{schedule:q,cancel:Lt,state:Ee,steps:va}=Lw(typeof requestAnimationFrame<"u"?requestAnimationFrame:le,!0),Dw={useVisualState:Zm({scrapeMotionValuesFromProps:Xm,createRenderState:Km,onMount:(e,t,{renderState:n,latestValues:r})=>{q.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),q.render(()=>{tc(n,r,{enableHardwareAcceleration:!1},nc(t.tagName),e.transformTemplate),Ym(t,n)})}})},Vw={useVisualState:Zm({scrapeMotionValuesFromProps:rc,createRenderState:ec})};function Iw(e,{forwardMotionProps:t=!1},n,r){return{...Zu(e)?Dw:Vw,preloadedFeatures:n,useRender:Tw(t),createVisualElement:r,Component:e}}function Et(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Jm=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function Is(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const Ow=e=>t=>Jm(t)&&e(t,Is(t));function Nt(e,t,n,r){return Et(e,t,Ow(n),r)}const Fw=(e,t)=>n=>t(e(n)),rn=(...e)=>e.reduce(Fw);function eg(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const qd=eg("dragHorizontal"),Zd=eg("dragVertical");function tg(e){let t=!1;if(e==="y")t=Zd();else if(e==="x")t=qd();else{const n=qd(),r=Zd();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function ng(){const e=tg(!0);return e?(e(),!1):!0}class gn{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Jd(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(o,s)=>{if(o.pointerType==="touch"||ng())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&q.update(()=>a[r](o,s))};return Nt(e.current,n,i,{passive:!e.getProps()[r]})}class zw extends gn{mount(){this.unmount=rn(Jd(this.node,!0),Jd(this.node,!1))}unmount(){}}class Bw extends gn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=rn(Et(this.node.current,"focus",()=>this.onFocus()),Et(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const rg=(e,t)=>t?e===t?!0:rg(e,t.parentElement):!1;function ya(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,Is(n))}class Uw extends gn{constructor(){super(...arguments),this.removeStartListeners=le,this.removeEndListeners=le,this.removeAccessibleListeners=le,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=Nt(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:d,globalTapTarget:c}=this.node.getProps();q.update(()=>{!c&&!rg(this.node.current,a.target)?d&&d(a,l):u&&u(a,l)})},{passive:!(r.onTap||r.onPointerUp)}),s=Nt(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=rn(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=a=>{a.key!=="Enter"||!this.checkPressEnd()||ya("up",(l,u)=>{const{onTap:d}=this.node.getProps();d&&q.update(()=>d(l,u))})};this.removeEndListeners(),this.removeEndListeners=Et(this.node.current,"keyup",s),ya("down",(a,l)=>{this.startPress(a,l)})},n=Et(this.node.current,"keydown",t),r=()=>{this.isPressing&&ya("cancel",(o,s)=>this.cancelPress(o,s))},i=Et(this.node.current,"blur",r);this.removeAccessibleListeners=rn(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&q.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!ng()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&q.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=Nt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Et(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=rn(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Rl=new WeakMap,xa=new WeakMap,$w=e=>{const t=Rl.get(e.target);t&&t(e)},Ww=e=>{e.forEach($w)};function Hw({root:e,...t}){const n=e||document;xa.has(n)||xa.set(n,{});const r=xa.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(Ww,{root:e,...t})),r[i]}function Kw(e,t,n){const r=Hw(t);return Rl.set(e,n),r.observe(e),()=>{Rl.delete(e),r.unobserve(e)}}const Gw={some:0,all:1};class Qw extends gn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:Gw[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:d,onViewportLeave:c}=this.node.getProps(),f=u?d:c;f&&f(l)};return Kw(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Yw(t,n))&&this.startObserver()}unmount(){}}function Yw({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Xw={inView:{Feature:Qw},tap:{Feature:Uw},focus:{Feature:Bw},hover:{Feature:zw}};function ig(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function qw(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function Zw(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Os(e,t,n){const r=e.getProps();return ic(r,t,n!==void 0?n:r.custom,qw(e),Zw(e))}let Jw=le,oc=le;const on=e=>e*1e3,jt=e=>e/1e3,eS={current:!1},og=e=>Array.isArray(e)&&typeof e[0]=="number";function sg(e){return!!(!e||typeof e=="string"&&ag[e]||og(e)||Array.isArray(e)&&e.every(sg))}const Gr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,ag={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Gr([0,.65,.55,1]),circOut:Gr([.55,0,1,.45]),backIn:Gr([.31,.01,.66,-.59]),backOut:Gr([.33,1.53,.69,.99])};function lg(e){if(e)return og(e)?Gr(e):Array.isArray(e)?e.map(lg):ag[e]}function tS(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const d=lg(a);return Array.isArray(d)&&(u.easing=d),e.animate(u,{delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}function nS(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const ug=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,rS=1e-7,iS=12;function oS(e,t,n,r,i){let o,s,a=0;do s=t+(n-t)/2,o=ug(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>rS&&++a<iS);return s}function Fi(e,t,n,r){if(e===t&&n===r)return le;const i=o=>oS(o,0,1,e,n);return o=>o===0||o===1?o:ug(i(o),t,r)}const sS=Fi(.42,0,1,1),aS=Fi(0,0,.58,1),cg=Fi(.42,0,.58,1),lS=e=>Array.isArray(e)&&typeof e[0]!="number",dg=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,fg=e=>t=>1-e(1-t),sc=e=>1-Math.sin(Math.acos(e)),pg=fg(sc),uS=dg(sc),hg=Fi(.33,1.53,.69,.99),ac=fg(hg),cS=dg(ac),dS=e=>(e*=2)<1?.5*ac(e):.5*(2-Math.pow(2,-10*(e-1))),fS={linear:le,easeIn:sS,easeInOut:cg,easeOut:aS,circIn:sc,circInOut:uS,circOut:pg,backIn:ac,backInOut:cS,backOut:hg,anticipate:dS},ef=e=>{if(Array.isArray(e)){oc(e.length===4);const[t,n,r,i]=e;return Fi(t,n,r,i)}else if(typeof e=="string")return fS[e];return e},lc=(e,t)=>n=>!!(Ii(n)&&pw.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),mg=(e,t,n)=>r=>{if(!Ii(r))return r;const[i,o,s,a]=r.match(Vs);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},pS=e=>un(0,255,e),wa={...Un,transform:e=>Math.round(pS(e))},Nn={test:lc("rgb","red"),parse:mg("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+wa.transform(e)+", "+wa.transform(t)+", "+wa.transform(n)+", "+ii(ri.transform(r))+")"};function hS(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Ml={test:lc("#"),parse:hS,transform:Nn.transform},sr={test:lc("hsl","hue"),parse:mg("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+St.transform(ii(t))+", "+St.transform(ii(n))+", "+ii(ri.transform(r))+")"},Re={test:e=>Nn.test(e)||Ml.test(e)||sr.test(e),parse:e=>Nn.test(e)?Nn.parse(e):sr.test(e)?sr.parse(e):Ml.parse(e),transform:e=>Ii(e)?e:e.hasOwnProperty("red")?Nn.transform(e):sr.transform(e)},ie=(e,t,n)=>-n*e+n*t+e;function Sa(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function mS({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=Sa(l,a,e+1/3),o=Sa(l,a,e),s=Sa(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const Ca=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},gS=[Ml,Nn,sr],vS=e=>gS.find(t=>t.test(e));function tf(e){const t=vS(e);let n=t.parse(e);return t===sr&&(n=mS(n)),n}const gg=(e,t)=>{const n=tf(e),r=tf(t),i={...n};return o=>(i.red=Ca(n.red,r.red,o),i.green=Ca(n.green,r.green,o),i.blue=Ca(n.blue,r.blue,o),i.alpha=ie(n.alpha,r.alpha,o),Nn.transform(i))};function yS(e){var t,n;return isNaN(e)&&Ii(e)&&(((t=e.match(Vs))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Um))===null||n===void 0?void 0:n.length)||0)>0}const vg={regex:dw,countKey:"Vars",token:"${v}",parse:le},yg={regex:Um,countKey:"Colors",token:"${c}",parse:Re.parse},xg={regex:Vs,countKey:"Numbers",token:"${n}",parse:Un.parse};function ka(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function os(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&ka(n,vg),ka(n,yg),ka(n,xg),n}function wg(e){return os(e).values}function Sg(e){const{values:t,numColors:n,numVars:r,tokenised:i}=os(e),o=t.length;return s=>{let a=i;for(let l=0;l<o;l++)l<r?a=a.replace(vg.token,s[l]):l<r+n?a=a.replace(yg.token,Re.transform(s[l])):a=a.replace(xg.token,ii(s[l]));return a}}const xS=e=>typeof e=="number"?0:e;function wS(e){const t=wg(e);return Sg(e)(t.map(xS))}const cn={test:yS,parse:wg,createTransformer:Sg,getAnimatableNone:wS},Cg=(e,t)=>n=>`${n>0?t:e}`;function kg(e,t){return typeof e=="number"?n=>ie(e,t,n):Re.test(e)?gg(e,t):e.startsWith("var(")?Cg(e,t):bg(e,t)}const Pg=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>kg(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},SS=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=kg(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},bg=(e,t)=>{const n=cn.createTransformer(t),r=os(e),i=os(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?rn(Pg(r.values,i.values),n):Cg(e,t)},Ni=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},nf=(e,t)=>n=>ie(e,t,n);function CS(e){return typeof e=="number"?nf:typeof e=="string"?Re.test(e)?gg:bg:Array.isArray(e)?Pg:typeof e=="object"?SS:nf}function kS(e,t,n){const r=[],i=n||CS(e[0]),o=e.length-1;for(let s=0;s<o;s++){let a=i(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]||le:t;a=rn(l,a)}r.push(a)}return r}function Eg(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(oc(o===t.length),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=kS(t,r,i),a=s.length,l=u=>{let d=0;if(a>1)for(;d<e.length-2&&!(u<e[d+1]);d++);const c=Ni(e[d],e[d+1],u);return s[d](c)};return n?u=>l(un(e[0],e[o-1],u)):l}function PS(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Ni(0,t,r);e.push(ie(n,1,i))}}function bS(e){const t=[0];return PS(t,e.length-1),t}function ES(e,t){return e.map(n=>n*t)}function TS(e,t){return e.map(()=>t||cg).splice(0,e.length-1)}function ss({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=lS(r)?r.map(ef):ef(r),o={done:!1,value:t[0]},s=ES(n&&n.length===t.length?n:bS(t),e),a=Eg(s,t,{ease:Array.isArray(i)?i:TS(t,i)});return{calculatedDuration:e,next:l=>(o.value=a(l),o.done=l>=e,o)}}function Tg(e,t){return t?e*(1e3/t):0}const NS=5;function Ng(e,t,n){const r=Math.max(t-NS,0);return Tg(n-e(r),t-r)}const Pa=.001,jS=.01,rf=10,RS=.05,MS=1;function AS({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o;Jw(e<=on(rf));let s=1-t;s=un(RS,MS,s),e=un(jS,rf,jt(e)),s<1?(i=u=>{const d=u*s,c=d*e,f=d-n,g=Al(u,s),w=Math.exp(-c);return Pa-f/g*w},o=u=>{const c=u*s*e,f=c*n+n,g=Math.pow(s,2)*Math.pow(u,2)*e,w=Math.exp(-c),x=Al(Math.pow(u,2),s);return(-i(u)+Pa>0?-1:1)*((f-g)*w)/x}):(i=u=>{const d=Math.exp(-u*e),c=(u-n)*e+1;return-Pa+d*c},o=u=>{const d=Math.exp(-u*e),c=(n-u)*(e*e);return d*c});const a=5/e,l=LS(i,o,a);if(e=on(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const _S=12;function LS(e,t,n){let r=n;for(let i=1;i<_S;i++)r=r-e(r)/t(r);return r}function Al(e,t){return e*Math.sqrt(1-t*t)}const DS=["duration","bounce"],VS=["stiffness","damping","mass"];function of(e,t){return t.some(n=>e[n]!==void 0)}function IS(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!of(e,VS)&&of(e,DS)){const n=AS(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function jg({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:a,damping:l,mass:u,duration:d,velocity:c,isResolvedFromDuration:f}=IS({...r,velocity:-jt(r.velocity||0)}),g=c||0,w=l/(2*Math.sqrt(a*u)),x=o-i,C=jt(Math.sqrt(a/u)),v=Math.abs(x)<5;n||(n=v?.01:2),t||(t=v?.005:.5);let p;if(w<1){const m=Al(C,w);p=S=>{const k=Math.exp(-w*C*S);return o-k*((g+w*C*x)/m*Math.sin(m*S)+x*Math.cos(m*S))}}else if(w===1)p=m=>o-Math.exp(-C*m)*(x+(g+C*x)*m);else{const m=C*Math.sqrt(w*w-1);p=S=>{const k=Math.exp(-w*C*S),b=Math.min(m*S,300);return o-k*((g+w*C*x)*Math.sinh(b)+m*x*Math.cosh(b))/m}}return{calculatedDuration:f&&d||null,next:m=>{const S=p(m);if(f)s.done=m>=d;else{let k=g;m!==0&&(w<1?k=Ng(p,m,S):k=0);const b=Math.abs(k)<=n,P=Math.abs(o-S)<=t;s.done=b&&P}return s.value=s.done?o:S,s}}}function sf({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:d}){const c=e[0],f={done:!1,value:c},g=E=>a!==void 0&&E<a||l!==void 0&&E>l,w=E=>a===void 0?l:l===void 0||Math.abs(a-E)<Math.abs(l-E)?a:l;let x=n*t;const C=c+x,v=s===void 0?C:s(C);v!==C&&(x=v-c);const p=E=>-x*Math.exp(-E/r),m=E=>v+p(E),S=E=>{const R=p(E),j=m(E);f.done=Math.abs(R)<=u,f.value=f.done?v:j};let k,b;const P=E=>{g(f.value)&&(k=E,b=jg({keyframes:[f.value,w(f.value)],velocity:Ng(m,E,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:d}))};return P(0),{calculatedDuration:null,next:E=>{let R=!1;return!b&&k===void 0&&(R=!0,S(E),P(E)),k!==void 0&&E>k?b.next(E-k):(!R&&S(E),f)}}}const OS=e=>{const t=({timestamp:n})=>e(n);return{start:()=>q.update(t,!0),stop:()=>Lt(t),now:()=>Ee.isProcessing?Ee.timestamp:performance.now()}},af=2e4;function lf(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<af;)t+=n,r=e.next(t);return t>=af?1/0:t}const FS={decay:sf,inertia:sf,tween:ss,keyframes:ss,spring:jg};function as({autoplay:e=!0,delay:t=0,driver:n=OS,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:d,onUpdate:c,...f}){let g=1,w=!1,x,C;const v=()=>{C=new Promise(V=>{x=V})};v();let p;const m=FS[i]||ss;let S;m!==ss&&typeof r[0]!="number"&&(S=Eg([0,100],r,{clamp:!1}),r=[0,100]);const k=m({...f,keyframes:r});let b;a==="mirror"&&(b=m({...f,keyframes:[...r].reverse(),velocity:-(f.velocity||0)}));let P="idle",E=null,R=null,j=null;k.calculatedDuration===null&&o&&(k.calculatedDuration=lf(k));const{calculatedDuration:O}=k;let D=1/0,H=1/0;O!==null&&(D=O+s,H=D*(o+1)-s);let A=0;const G=V=>{if(R===null)return;g>0&&(R=Math.min(R,V)),g<0&&(R=Math.min(V-H/g,R)),E!==null?A=E:A=Math.round(V-R)*g;const $=A-t*(g>=0?1:-1),Y=g>=0?$<0:$>H;A=Math.max($,0),P==="finished"&&E===null&&(A=H);let Ue=A,Wn=k;if(o){const $s=Math.min(A,H)/D;let Ui=Math.floor($s),yn=$s%1;!yn&&$s>=1&&(yn=1),yn===1&&Ui--,Ui=Math.min(Ui,o+1),!!(Ui%2)&&(a==="reverse"?(yn=1-yn,s&&(yn-=s/D)):a==="mirror"&&(Wn=b)),Ue=un(0,1,yn)*D}const $e=Y?{done:!1,value:r[0]}:Wn.next(Ue);S&&($e.value=S($e.value));let{done:vn}=$e;!Y&&O!==null&&(vn=g>=0?A>=H:A<=0);const yy=E===null&&(P==="finished"||P==="running"&&vn);return c&&c($e.value),yy&&N(),$e},z=()=>{p&&p.stop(),p=void 0},Q=()=>{P="idle",z(),x(),v(),R=j=null},N=()=>{P="finished",d&&d(),z(),x()},_=()=>{if(w)return;p||(p=n(G));const V=p.now();l&&l(),E!==null?R=V-E:(!R||P==="finished")&&(R=V),P==="finished"&&v(),j=R,E=null,P="running",p.start()};e&&_();const F={then(V,$){return C.then(V,$)},get time(){return jt(A)},set time(V){V=on(V),A=V,E!==null||!p||g===0?E=V:R=p.now()-V/g},get duration(){const V=k.calculatedDuration===null?lf(k):k.calculatedDuration;return jt(V)},get speed(){return g},set speed(V){V===g||!p||(g=V,F.time=jt(A))},get state(){return P},play:_,pause:()=>{P="paused",E=A},stop:()=>{w=!0,P!=="idle"&&(P="idle",u&&u(),Q())},cancel:()=>{j!==null&&G(j),Q()},complete:()=>{P="finished"},sample:V=>(R=0,G(V))};return F}function zS(e){let t;return()=>(t===void 0&&(t=e()),t)}const BS=zS(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),US=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),lo=10,$S=2e4,WS=(e,t)=>t.type==="spring"||e==="backgroundColor"||!sg(t.ease);function HS(e,t,{onUpdate:n,onComplete:r,...i}){if(!(BS()&&US.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let s=!1,a,l,u=!1;const d=()=>{l=new Promise(m=>{a=m})};d();let{keyframes:c,duration:f=300,ease:g,times:w}=i;if(WS(t,i)){const m=as({...i,repeat:0,delay:0});let S={done:!1,value:c[0]};const k=[];let b=0;for(;!S.done&&b<$S;)S=m.sample(b),k.push(S.value),b+=lo;w=void 0,c=k,f=b-lo,g="linear"}const x=tS(e.owner.current,t,c,{...i,duration:f,ease:g,times:w}),C=()=>{u=!1,x.cancel()},v=()=>{u=!0,q.update(C),a(),d()};return x.onfinish=()=>{u||(e.set(nS(c,i)),r&&r(),v())},{then(m,S){return l.then(m,S)},attachTimeline(m){return x.timeline=m,x.onfinish=null,le},get time(){return jt(x.currentTime||0)},set time(m){x.currentTime=on(m)},get speed(){return x.playbackRate},set speed(m){x.playbackRate=m},get duration(){return jt(f)},play:()=>{s||(x.play(),Lt(C))},pause:()=>x.pause(),stop:()=>{if(s=!0,x.playState==="idle")return;const{currentTime:m}=x;if(m){const S=as({...i,autoplay:!1});e.setWithVelocity(S.sample(m-lo).value,S.sample(m).value,lo)}v()},complete:()=>{u||x.finish()},cancel:v}}function KS({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:le,pause:le,stop:le,then:o=>(o(),Promise.resolve()),cancel:le,complete:le});return t?as({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const GS={type:"spring",stiffness:500,damping:25,restSpeed:10},QS=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),YS={type:"keyframes",duration:.8},XS={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},qS=(e,{keyframes:t})=>t.length>2?YS:Bn.has(e)?e.startsWith("scale")?QS(t[1]):GS:XS,_l=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(cn.test(t)||t==="0")&&!t.startsWith("url(")),ZS=new Set(["brightness","contrast","saturate","opacity"]);function JS(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Vs)||[];if(!r)return e;const i=n.replace(r,"");let o=ZS.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const eC=/([a-z-]*)\(.*?\)/g,Ll={...cn,getAnimatableNone:e=>{const t=e.match(eC);return t?t.map(JS).join(" "):e}},tC={...$m,color:Re,backgroundColor:Re,outlineColor:Re,fill:Re,stroke:Re,borderColor:Re,borderTopColor:Re,borderRightColor:Re,borderBottomColor:Re,borderLeftColor:Re,filter:Ll,WebkitFilter:Ll},uc=e=>tC[e];function Rg(e,t){let n=uc(e);return n!==Ll&&(n=cn),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Mg=e=>/^0[^.\s]+$/.test(e);function nC(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||Mg(e)}function rC(e,t,n,r){const i=_l(t,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const s=r.from!==void 0?r.from:e.get();let a;const l=[];for(let u=0;u<o.length;u++)o[u]===null&&(o[u]=u===0?s:o[u-1]),nC(o[u])&&l.push(u),typeof o[u]=="string"&&o[u]!=="none"&&o[u]!=="0"&&(a=o[u]);if(i&&l.length&&a)for(let u=0;u<l.length;u++){const d=l[u];o[d]=Rg(t,a)}return o}function iC({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...d}){return!!Object.keys(d).length}function cc(e,t){return e[t]||e.default||e}const oC={skipAnimations:!1},dc=(e,t,n,r={})=>i=>{const o=cc(r,e)||{},s=o.delay||r.delay||0;let{elapsed:a=0}=r;a=a-on(s);const l=rC(t,e,n,o),u=l[0],d=l[l.length-1],c=_l(e,u),f=_l(e,d);let g={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:w=>{t.set(w),o.onUpdate&&o.onUpdate(w)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(iC(o)||(g={...g,...qS(e,g)}),g.duration&&(g.duration=on(g.duration)),g.repeatDelay&&(g.repeatDelay=on(g.repeatDelay)),!c||!f||eS.current||o.type===!1||oC.skipAnimations)return KS(g);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const w=HS(t,e,g);if(w)return w}return as(g)};function ls(e){return!!(Be(e)&&e.add)}const Ag=e=>/^\-?\d*\.?\d+$/.test(e);function fc(e,t){e.indexOf(t)===-1&&e.push(t)}function pc(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class hc{constructor(){this.subscriptions=[]}add(t){return fc(this.subscriptions,t),()=>pc(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const sC=e=>!isNaN(parseFloat(e));class aC{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=Ee;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,q.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>q.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=sC(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new hc);const r=this.events[t].add(n);return t==="change"?()=>{r(),q.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Tg(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function kr(e,t){return new aC(e,t)}const _g=e=>t=>t.test(e),lC={test:e=>e==="auto",parse:e=>e},Lg=[Un,I,St,Ot,mw,hw,lC],Fr=e=>Lg.find(_g(e)),uC=[...Lg,Re,cn],cC=e=>uC.find(_g(e));function dC(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,kr(n))}function fC(e,t){const n=Os(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const a=jw(o[s]);dC(e,s,a)}}function pC(e,t,n){var r,i;const o=Object.keys(t).filter(a=>!e.hasValue(a)),s=o.length;if(s)for(let a=0;a<s;a++){const l=o[a],u=t[l];let d=null;Array.isArray(u)&&(d=u[0]),d===null&&(d=(i=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&i!==void 0?i:t[l]),d!=null&&(typeof d=="string"&&(Ag(d)||Mg(d))?d=parseFloat(d):!cC(d)&&cn.test(u)&&(d=Rg(l,u)),e.addValue(l,kr(d,{owner:e})),n[l]===void 0&&(n[l]=d),d!==null&&e.setBaseTarget(l,d))}}function hC(e,t){return t?(t[e]||t.default||t).from:void 0}function mC(e,t,n){const r={};for(const i in e){const o=hC(i,t);if(o!==void 0)r[i]=o;else{const s=n.getValue(i);s&&(r[i]=s.get())}}return r}function gC({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function vC(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Dg(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...a}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(o=r);const u=[],d=i&&e.animationState&&e.animationState.getState()[i];for(const c in a){const f=e.getValue(c),g=a[c];if(!f||g===void 0||d&&gC(d,c))continue;const w={delay:n,elapsed:0,...cc(o||{},c)};if(window.HandoffAppearAnimations){const v=e.getProps()[Vm];if(v){const p=window.HandoffAppearAnimations(v,c,f,q);p!==null&&(w.elapsed=p,w.isHandoff=!0)}}let x=!w.isHandoff&&!vC(f,g);if(w.type==="spring"&&(f.getVelocity()||w.velocity)&&(x=!1),f.animation&&(x=!1),x)continue;f.start(dc(c,f,g,e.shouldReduceMotion&&Bn.has(c)?{type:!1}:w));const C=f.animation;ls(l)&&(l.add(c),C.then(()=>l.remove(c))),u.push(C)}return s&&Promise.all(u).then(()=>{s&&fC(e,s)}),u}function Dl(e,t,n={}){const r=Os(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(Dg(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:d,staggerDirection:c}=i;return yC(e,t,u+l,d,c,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[l,u]=a==="beforeChildren"?[o,s]:[s,o];return l().then(()=>u())}else return Promise.all([o(),s(n.delay)])}function yC(e,t,n=0,r=0,i=1,o){const s=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(xC).forEach((u,d)=>{u.notify("AnimationStart",t),s.push(Dl(u,t,{...o,delay:n+l(d)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function xC(e,t){return e.sortNodePosition(t)}function wC(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>Dl(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=Dl(e,t,n);else{const i=typeof t=="function"?Os(e,t,n.custom):t;r=Promise.all(Dg(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const SC=[...Yu].reverse(),CC=Yu.length;function kC(e){return t=>Promise.all(t.map(({animation:n,options:r})=>wC(e,n,r)))}function PC(e){let t=kC(e);const n=EC();let r=!0;const i=(l,u)=>{const d=Os(e,u);if(d){const{transition:c,transitionEnd:f,...g}=d;l={...l,...g,...f}}return l};function o(l){t=l(e)}function s(l,u){const d=e.getProps(),c=e.getVariantContext(!0)||{},f=[],g=new Set;let w={},x=1/0;for(let v=0;v<CC;v++){const p=SC[v],m=n[p],S=d[p]!==void 0?d[p]:c[p],k=Ei(S),b=p===u?m.isActive:null;b===!1&&(x=v);let P=S===c[p]&&S!==d[p]&&k;if(P&&r&&e.manuallyAnimateOnMount&&(P=!1),m.protectedKeys={...w},!m.isActive&&b===null||!S&&!m.prevProp||Ls(S)||typeof S=="boolean")continue;let R=bC(m.prevProp,S)||p===u&&m.isActive&&!P&&k||v>x&&k,j=!1;const O=Array.isArray(S)?S:[S];let D=O.reduce(i,{});b===!1&&(D={});const{prevResolvedValues:H={}}=m,A={...H,...D},G=z=>{R=!0,g.has(z)&&(j=!0,g.delete(z)),m.needsAnimating[z]=!0};for(const z in A){const Q=D[z],N=H[z];if(w.hasOwnProperty(z))continue;let _=!1;is(Q)&&is(N)?_=!ig(Q,N):_=Q!==N,_?Q!==void 0?G(z):g.add(z):Q!==void 0&&g.has(z)?G(z):m.protectedKeys[z]=!0}m.prevProp=S,m.prevResolvedValues=D,m.isActive&&(w={...w,...D}),r&&e.blockInitialAnimation&&(R=!1),R&&(!P||j)&&f.push(...O.map(z=>({animation:z,options:{type:p,...l}})))}if(g.size){const v={};g.forEach(p=>{const m=e.getBaseTarget(p);m!==void 0&&(v[p]=m)}),f.push({animation:v})}let C=!!f.length;return r&&(d.initial===!1||d.initial===d.animate)&&!e.manuallyAnimateOnMount&&(C=!1),r=!1,C?t(f):Promise.resolve()}function a(l,u,d){var c;if(n[l].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(g=>{var w;return(w=g.animationState)===null||w===void 0?void 0:w.setActive(l,u)}),n[l].isActive=u;const f=s(d,l);for(const g in n)n[g].protectedKeys={};return f}return{animateChanges:s,setActive:a,setAnimateFunction:o,getState:()=>n}}function bC(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!ig(t,e):!1}function xn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function EC(){return{animate:xn(!0),whileInView:xn(),whileHover:xn(),whileTap:xn(),whileDrag:xn(),whileFocus:xn(),exit:xn()}}class TC extends gn{constructor(t){super(t),t.animationState||(t.animationState=PC(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Ls(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let NC=0;class jC extends gn{constructor(){super(...arguments),this.id=NC++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const o=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const RC={animation:{Feature:TC},exit:{Feature:jC}},uf=(e,t)=>Math.abs(e-t);function MC(e,t){const n=uf(e.x,t.x),r=uf(e.y,t.y);return Math.sqrt(n**2+r**2)}class Vg{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const c=Ea(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,g=MC(c.offset,{x:0,y:0})>=3;if(!f&&!g)return;const{point:w}=c,{timestamp:x}=Ee;this.history.push({...w,timestamp:x});const{onStart:C,onMove:v}=this.handlers;f||(C&&C(this.lastMoveEvent,c),this.startEvent=this.lastMoveEvent),v&&v(this.lastMoveEvent,c)},this.handlePointerMove=(c,f)=>{this.lastMoveEvent=c,this.lastMoveEventInfo=ba(f,this.transformPagePoint),q.update(this.updatePoint,!0)},this.handlePointerUp=(c,f)=>{this.end();const{onEnd:g,onSessionEnd:w,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const C=Ea(c.type==="pointercancel"?this.lastMoveEventInfo:ba(f,this.transformPagePoint),this.history);this.startEvent&&g&&g(c,C),w&&w(c,C)},!Jm(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=Is(t),a=ba(s,this.transformPagePoint),{point:l}=a,{timestamp:u}=Ee;this.history=[{...l,timestamp:u}];const{onSessionStart:d}=n;d&&d(t,Ea(a,this.history)),this.removeListeners=rn(Nt(this.contextWindow,"pointermove",this.handlePointerMove),Nt(this.contextWindow,"pointerup",this.handlePointerUp),Nt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Lt(this.updatePoint)}}function ba(e,t){return t?{point:t(e.point)}:e}function cf(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Ea({point:e},t){return{point:e,delta:cf(e,Ig(t)),offset:cf(e,AC(t)),velocity:_C(t,.1)}}function AC(e){return e[0]}function Ig(e){return e[e.length-1]}function _C(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Ig(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>on(t)));)n--;if(!r)return{x:0,y:0};const o=jt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function Qe(e){return e.max-e.min}function Vl(e,t=0,n=.01){return Math.abs(e-t)<=n}function df(e,t,n,r=.5){e.origin=r,e.originPoint=ie(t.min,t.max,e.origin),e.scale=Qe(n)/Qe(t),(Vl(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=ie(n.min,n.max,e.origin)-e.originPoint,(Vl(e.translate)||isNaN(e.translate))&&(e.translate=0)}function oi(e,t,n,r){df(e.x,t.x,n.x,r?r.originX:void 0),df(e.y,t.y,n.y,r?r.originY:void 0)}function ff(e,t,n){e.min=n.min+t.min,e.max=e.min+Qe(t)}function LC(e,t,n){ff(e.x,t.x,n.x),ff(e.y,t.y,n.y)}function pf(e,t,n){e.min=t.min-n.min,e.max=e.min+Qe(t)}function si(e,t,n){pf(e.x,t.x,n.x),pf(e.y,t.y,n.y)}function DC(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?ie(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?ie(n,e,r.max):Math.min(e,n)),e}function hf(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function VC(e,{top:t,left:n,bottom:r,right:i}){return{x:hf(e.x,n,i),y:hf(e.y,t,r)}}function mf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function IC(e,t){return{x:mf(e.x,t.x),y:mf(e.y,t.y)}}function OC(e,t){let n=.5;const r=Qe(e),i=Qe(t);return i>r?n=Ni(t.min,t.max-r,e.min):r>i&&(n=Ni(e.min,e.max-i,t.min)),un(0,1,n)}function FC(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Il=.35;function zC(e=Il){return e===!1?e=0:e===!0&&(e=Il),{x:gf(e,"left","right"),y:gf(e,"top","bottom")}}function gf(e,t,n){return{min:vf(e,t),max:vf(e,n)}}function vf(e,t){return typeof e=="number"?e:e[t]||0}const yf=()=>({translate:0,scale:1,origin:0,originPoint:0}),ar=()=>({x:yf(),y:yf()}),xf=()=>({min:0,max:0}),he=()=>({x:xf(),y:xf()});function Je(e){return[e("x"),e("y")]}function Og({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function BC({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function UC(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Ta(e){return e===void 0||e===1}function Ol({scale:e,scaleX:t,scaleY:n}){return!Ta(e)||!Ta(t)||!Ta(n)}function Cn(e){return Ol(e)||Fg(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Fg(e){return wf(e.x)||wf(e.y)}function wf(e){return e&&e!=="0%"}function us(e,t,n){const r=e-n,i=t*r;return n+i}function Sf(e,t,n,r,i){return i!==void 0&&(e=us(e,i,r)),us(e,n,r)+t}function Fl(e,t=0,n=1,r,i){e.min=Sf(e.min,t,n,r,i),e.max=Sf(e.max,t,n,r,i)}function zg(e,{x:t,y:n}){Fl(e.x,t.translate,t.scale,t.originPoint),Fl(e.y,n.translate,n.scale,n.originPoint)}function $C(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let a=0;a<i;a++){o=n[a],s=o.projectionDelta;const l=o.instance;l&&l.style&&l.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&lr(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,zg(e,s)),r&&Cn(o.latestValues)&&lr(e,o.latestValues))}t.x=Cf(t.x),t.y=Cf(t.y)}function Cf(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Ut(e,t){e.min=e.min+t,e.max=e.max+t}function kf(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=ie(e.min,e.max,o);Fl(e,t[n],t[r],s,t.scale)}const WC=["x","scaleX","originX"],HC=["y","scaleY","originY"];function lr(e,t){kf(e.x,t,WC),kf(e.y,t,HC)}function Bg(e,t){return Og(UC(e.getBoundingClientRect(),t))}function KC(e,t,n){const r=Bg(e,n),{scroll:i}=t;return i&&(Ut(r.x,i.offset.x),Ut(r.y,i.offset.y)),r}const Ug=({current:e})=>e?e.ownerDocument.defaultView:null,GC=new WeakMap;class QC{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=he(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=d=>{const{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Is(d,"page").point)},o=(d,c)=>{const{drag:f,dragPropagation:g,onDragStart:w}=this.getProps();if(f&&!g&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=tg(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Je(C=>{let v=this.getAxisMotionValue(C).get()||0;if(St.test(v)){const{projection:p}=this.visualElement;if(p&&p.layout){const m=p.layout.layoutBox[C];m&&(v=Qe(m)*(parseFloat(v)/100))}}this.originPoint[C]=v}),w&&q.update(()=>w(d,c),!1,!0);const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},s=(d,c)=>{const{dragPropagation:f,dragDirectionLock:g,onDirectionLock:w,onDrag:x}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:C}=c;if(g&&this.currentDirection===null){this.currentDirection=YC(C),this.currentDirection!==null&&w&&w(this.currentDirection);return}this.updateAxis("x",c.point,C),this.updateAxis("y",c.point,C),this.visualElement.render(),x&&x(d,c)},a=(d,c)=>this.stop(d,c),l=()=>Je(d=>{var c;return this.getAnimationState(d)==="paused"&&((c=this.getAxisMotionValue(d).animation)===null||c===void 0?void 0:c.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Vg(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Ug(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&q.update(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!uo(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=DC(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&or(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=VC(i.layoutBox,n):this.constraints=!1,this.elastic=zC(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Je(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=FC(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!or(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=KC(r,i.root,this.visualElement.getTransformPagePoint());let s=IC(i.layout.layoutBox,o);if(n){const a=n(BC(s));this.hasMutatedConstraints=!!a,a&&(s=Og(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Je(d=>{if(!uo(d,n,this.currentDirection))return;let c=l&&l[d]||{};s&&(c={min:0,max:0});const f=i?200:1e6,g=i?40:1e7,w={type:"inertia",velocity:r?t[d]:0,bounceStiffness:f,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...o,...c};return this.startAxisValueAnimation(d,w)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(dc(t,r,0,n))}stopAnimation(){Je(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Je(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Je(n=>{const{drag:r}=this.getProps();if(!uo(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:a}=i.layout.layoutBox[n];o.set(t[n]-ie(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!or(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Je(s=>{const a=this.getAxisMotionValue(s);if(a){const l=a.get();i[s]=OC({min:l,max:l},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Je(s=>{if(!uo(s,t,null))return;const a=this.getAxisMotionValue(s),{min:l,max:u}=this.constraints[s];a.set(ie(l,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;GC.set(this.visualElement,this);const t=this.visualElement.current,n=Nt(t,"pointerdown",l=>{const{drag:u,dragListener:d=!0}=this.getProps();u&&d&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();or(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const s=Et(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Je(d=>{const c=this.getAxisMotionValue(d);c&&(this.originPoint[d]+=l[d].translate,c.set(c.get()+l[d].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=Il,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:a}}}function uo(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function YC(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class XC extends gn{constructor(t){super(t),this.removeGroupControls=le,this.removeListeners=le,this.controls=new QC(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||le}unmount(){this.removeGroupControls(),this.removeListeners()}}const Pf=e=>(t,n)=>{e&&q.update(()=>e(t,n))};class qC extends gn{constructor(){super(...arguments),this.removePointerDownListener=le}onPointerDown(t){this.session=new Vg(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Ug(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Pf(t),onStart:Pf(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&q.update(()=>i(o,s))}}}mount(){this.removePointerDownListener=Nt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function ZC(){const e=y.useContext(As);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=y.useId();return y.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const To={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function bf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const zr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(I.test(e))e=parseFloat(e);else return e;const n=bf(e,t.target.x),r=bf(e,t.target.y);return`${n}% ${r}%`}},JC={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=cn.parse(e);if(i.length>5)return r;const o=cn.createTransformer(e),s=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;i[0+s]/=a,i[1+s]/=l;const u=ie(a,l,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class ek extends re.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;aw(tk),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),To.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||q.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function $g(e){const[t,n]=ZC(),r=y.useContext(qu);return re.createElement(ek,{...e,layoutGroup:r,switchLayoutGroup:y.useContext(Om),isPresent:t,safeToRemove:n})}const tk={borderRadius:{...zr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:zr,borderTopRightRadius:zr,borderBottomLeftRadius:zr,borderBottomRightRadius:zr,boxShadow:JC},Wg=["TopLeft","TopRight","BottomLeft","BottomRight"],nk=Wg.length,Ef=e=>typeof e=="string"?parseFloat(e):e,Tf=e=>typeof e=="number"||I.test(e);function rk(e,t,n,r,i,o){i?(e.opacity=ie(0,n.opacity!==void 0?n.opacity:1,ik(r)),e.opacityExit=ie(t.opacity!==void 0?t.opacity:1,0,ok(r))):o&&(e.opacity=ie(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<nk;s++){const a=`border${Wg[s]}Radius`;let l=Nf(t,a),u=Nf(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||Tf(l)===Tf(u)?(e[a]=Math.max(ie(Ef(l),Ef(u),r),0),(St.test(u)||St.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=ie(t.rotate||0,n.rotate||0,r))}function Nf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const ik=Hg(0,.5,pg),ok=Hg(.5,.95,le);function Hg(e,t,n){return r=>r<e?0:r>t?1:n(Ni(e,t,r))}function jf(e,t){e.min=t.min,e.max=t.max}function Ze(e,t){jf(e.x,t.x),jf(e.y,t.y)}function Rf(e,t,n,r,i){return e-=t,e=us(e,1/n,r),i!==void 0&&(e=us(e,1/i,r)),e}function sk(e,t=0,n=1,r=.5,i,o=e,s=e){if(St.test(t)&&(t=parseFloat(t),t=ie(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=ie(o.min,o.max,r);e===o&&(a-=t),e.min=Rf(e.min,t,n,a,i),e.max=Rf(e.max,t,n,a,i)}function Mf(e,t,[n,r,i],o,s){sk(e,t[n],t[r],t[i],t.scale,o,s)}const ak=["x","scaleX","originX"],lk=["y","scaleY","originY"];function Af(e,t,n,r){Mf(e.x,t,ak,n?n.x:void 0,r?r.x:void 0),Mf(e.y,t,lk,n?n.y:void 0,r?r.y:void 0)}function _f(e){return e.translate===0&&e.scale===1}function Kg(e){return _f(e.x)&&_f(e.y)}function uk(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Gg(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Lf(e){return Qe(e.x)/Qe(e.y)}class ck{constructor(){this.members=[]}add(t){fc(this.members,t),t.scheduleRender()}remove(t){if(pc(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Df(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:u,rotateY:d}=n;l&&(r+=`rotate(${l}deg) `),u&&(r+=`rotateX(${u}deg) `),d&&(r+=`rotateY(${d}deg) `)}const s=e.x.scale*t.x,a=e.y.scale*t.y;return(s!==1||a!==1)&&(r+=`scale(${s}, ${a})`),r||"none"}const dk=(e,t)=>e.depth-t.depth;class fk{constructor(){this.children=[],this.isDirty=!1}add(t){fc(this.children,t),this.isDirty=!0}remove(t){pc(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(dk),this.isDirty=!1,this.children.forEach(t)}}function pk(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(Lt(r),e(o-t))};return q.read(r,!0),()=>Lt(r)}function hk(e){window.MotionDebug&&window.MotionDebug.record(e)}function mk(e){return e instanceof SVGElement&&e.tagName!=="svg"}function gk(e,t,n){const r=Be(e)?e:kr(e);return r.start(dc("",r,t,n)),r.animation}const Vf=["","X","Y","Z"],vk={visibility:"hidden"},If=1e3;let yk=0;const kn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Qg({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},a=t==null?void 0:t()){this.id=yk++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,kn.totalNodes=kn.resolvedTargetDeltas=kn.recalculatedProjection=0,this.nodes.forEach(Sk),this.nodes.forEach(Ek),this.nodes.forEach(Tk),this.nodes.forEach(Ck),hk(kn)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new fk)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new hc),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l&&l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=mk(s),this.instance=s;const{layoutId:l,layout:u,visualElement:d}=this.options;if(d&&!d.current&&d.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let c;const f=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=pk(f,250),To.hasAnimatedSinceResize&&(To.hasAnimatedSinceResize=!1,this.nodes.forEach(Ff))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&d&&(l||u)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:f,hasRelativeTargetChanged:g,layout:w})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||d.getDefaultTransition()||Ak,{onLayoutAnimationStart:C,onLayoutAnimationComplete:v}=d.getProps(),p=!this.targetLayout||!Gg(this.targetLayout,w)||g,m=!f&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||m||f&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(c,m);const S={...cc(x,"layout"),onPlay:C,onComplete:v};(d.shouldReduceMotion||this.options.layoutRoot)&&(S.delay=0,S.type=!1),this.startAnimation(S)}else f||Ff(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=w})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Lt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Nk),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let d=0;d<this.path.length;d++){const c=this.path[d];c.shouldResetTransform=!0,c.updateScroll("snapshot"),c.options.layoutRoot&&c.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Of);return}this.isUpdating||this.nodes.forEach(Pk),this.isUpdating=!1,this.nodes.forEach(bk),this.nodes.forEach(xk),this.nodes.forEach(wk),this.clearAllSnapshots();const a=performance.now();Ee.delta=un(0,1e3/60,a-Ee.timestamp),Ee.timestamp=a,Ee.isProcessing=!0,va.update.process(Ee),va.preRender.process(Ee),va.render.process(Ee),Ee.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(kk),this.sharedNodes.forEach(jk)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,q.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){q.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=he(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!Kg(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,d=u!==this.prevTransformTemplateValue;s&&(a||Cn(this.latestValues)||d)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),_k(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return he();const a=s.measureViewportBox(),{scroll:l}=this.root;return l&&(Ut(a.x,l.offset.x),Ut(a.y,l.offset.y)),a}removeElementScroll(s){const a=he();Ze(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:d,options:c}=u;if(u!==this.root&&d&&c.layoutScroll){if(d.isRoot){Ze(a,s);const{scroll:f}=this.root;f&&(Ut(a.x,-f.offset.x),Ut(a.y,-f.offset.y))}Ut(a.x,d.offset.x),Ut(a.y,d.offset.y)}}return a}applyTransform(s,a=!1){const l=he();Ze(l,s);for(let u=0;u<this.path.length;u++){const d=this.path[u];!a&&d.options.layoutScroll&&d.scroll&&d!==d.root&&lr(l,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),Cn(d.latestValues)&&lr(l,d.latestValues)}return Cn(this.latestValues)&&lr(l,this.latestValues),l}removeTransform(s){const a=he();Ze(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Cn(u.latestValues))continue;Ol(u.latestValues)&&u.updateSnapshot();const d=he(),c=u.measurePageBox();Ze(d,c),Af(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,d)}return Cn(this.latestValues)&&Af(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Ee.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:c,layoutId:f}=this.options;if(!(!this.layout||!(c||f))){if(this.resolvedRelativeTargetAt=Ee.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=he(),this.relativeTargetOrigin=he(),si(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),Ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=he(),this.targetWithTransforms=he()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),LC(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ze(this.target,this.layout.layoutBox),zg(this.target,this.targetDelta)):Ze(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=he(),this.relativeTargetOrigin=he(),si(this.relativeTargetOrigin,this.target,g.target),Ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}kn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ol(this.parent.latestValues)||Fg(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===Ee.timestamp&&(u=!1),u)return;const{layout:d,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||c))return;Ze(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,g=this.treeScale.y;$C(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:w}=a;if(!w){this.projectionTransform&&(this.projectionDelta=ar(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=ar(),this.projectionDeltaWithTransform=ar());const x=this.projectionTransform;oi(this.projectionDelta,this.layoutCorrected,w,this.latestValues),this.projectionTransform=Df(this.projectionDelta,this.treeScale),(this.projectionTransform!==x||this.treeScale.x!==f||this.treeScale.y!==g)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",w)),kn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,a=!1){const l=this.snapshot,u=l?l.latestValues:{},d={...this.latestValues},c=ar();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=he(),g=l?l.source:void 0,w=this.layout?this.layout.source:void 0,x=g!==w,C=this.getStack(),v=!C||C.members.length<=1,p=!!(x&&!v&&this.options.crossfade===!0&&!this.path.some(Mk));this.animationProgress=0;let m;this.mixTargetDelta=S=>{const k=S/1e3;zf(c.x,s.x,k),zf(c.y,s.y,k),this.setTargetDelta(c),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(si(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Rk(this.relativeTarget,this.relativeTargetOrigin,f,k),m&&uk(this.relativeTarget,m)&&(this.isProjectionDirty=!1),m||(m=he()),Ze(m,this.relativeTarget)),x&&(this.animationValues=d,rk(d,u,this.latestValues,k,p,v)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=k},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Lt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=q.update(()=>{To.hasAnimatedSinceResize=!0,this.currentAnimation=gk(0,If,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(If),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:d}=s;if(!(!a||!l||!u)){if(this!==s&&this.layout&&u&&Yg(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||he();const c=Qe(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+c;const f=Qe(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+f}Ze(a,l),lr(a,d),oi(this.projectionDeltaWithTransform,this.layoutCorrected,a,d)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new ck),this.sharedNodes.get(s).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const u={};for(let d=0;d<Vf.length;d++){const c="rotate"+Vf[d];l[c]&&(u[c]=l[c],s.setStaticValue(c,0))}s.render();for(const d in u)s.setStaticValue(d,u[d]);s.scheduleRender()}getProjectionStyles(s){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return vk;const u={visibility:""},d=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Eo(s==null?void 0:s.pointerEvents)||"",u.transform=d?d(this.latestValues,""):"none",u;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=Eo(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!Cn(this.latestValues)&&(x.transform=d?d({},""):"none",this.hasProjected=!1),x}const f=c.animationValues||c.latestValues;this.applyTransformsToTarget(),u.transform=Df(this.projectionDeltaWithTransform,this.treeScale,f),d&&(u.transform=d(f,u.transform));const{x:g,y:w}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${w.origin*100}% 0`,c.animationValues?u.opacity=c===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=c===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const x in ns){if(f[x]===void 0)continue;const{correct:C,applyTo:v}=ns[x],p=u.transform==="none"?f[x]:C(f[x],c);if(v){const m=v.length;for(let S=0;S<m;S++)u[v[S]]=p}else u[x]=p}return this.options.layoutId&&(u.pointerEvents=c===this?Eo(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Of),this.root.sharedNodes.clear()}}}function xk(e){e.updateLayout()}function wk(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?Je(c=>{const f=s?n.measuredBox[c]:n.layoutBox[c],g=Qe(f);f.min=r[c].min,f.max=f.min+g}):Yg(o,n.layoutBox,r)&&Je(c=>{const f=s?n.measuredBox[c]:n.layoutBox[c],g=Qe(r[c]);f.max=f.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[c].max=e.relativeTarget[c].min+g)});const a=ar();oi(a,r,n.layoutBox);const l=ar();s?oi(l,e.applyTransform(i,!0),n.measuredBox):oi(l,r,n.layoutBox);const u=!Kg(a);let d=!1;if(!e.resumeFrom){const c=e.getClosestProjectingParent();if(c&&!c.resumeFrom){const{snapshot:f,layout:g}=c;if(f&&g){const w=he();si(w,n.layoutBox,f.layoutBox);const x=he();si(x,r,g.layoutBox),Gg(w,x)||(d=!0),c.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=w,e.relativeParent=c)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:d})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function Sk(e){kn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Ck(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function kk(e){e.clearSnapshot()}function Of(e){e.clearMeasurements()}function Pk(e){e.isLayoutDirty=!1}function bk(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ff(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Ek(e){e.resolveTargetDelta()}function Tk(e){e.calcProjection()}function Nk(e){e.resetRotation()}function jk(e){e.removeLeadSnapshot()}function zf(e,t,n){e.translate=ie(t.translate,0,n),e.scale=ie(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Bf(e,t,n,r){e.min=ie(t.min,n.min,r),e.max=ie(t.max,n.max,r)}function Rk(e,t,n,r){Bf(e.x,t.x,n.x,r),Bf(e.y,t.y,n.y,r)}function Mk(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Ak={duration:.45,ease:[.4,0,.1,1]},Uf=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),$f=Uf("applewebkit/")&&!Uf("chrome/")?Math.round:le;function Wf(e){e.min=$f(e.min),e.max=$f(e.max)}function _k(e){Wf(e.x),Wf(e.y)}function Yg(e,t,n){return e==="position"||e==="preserve-aspect"&&!Vl(Lf(t),Lf(n),.2)}const Lk=Qg({attachResizeListener:(e,t)=>Et(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Na={current:void 0},Xg=Qg({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Na.current){const e=new Lk({});e.mount(window),e.setOptions({layoutScroll:!0}),Na.current=e}return Na.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Dk={pan:{Feature:qC},drag:{Feature:XC,ProjectionNode:Xg,MeasureLayout:$g}},Vk=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Ik(e){const t=Vk.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function zl(e,t,n=1){const[r,i]=Ik(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return Ag(s)?parseFloat(s):s}else return jl(i)?zl(i,t,n+1):i}function Ok(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!jl(o))return;const s=zl(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!jl(o))continue;const s=zl(o,r);s&&(t[i]=s,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const Fk=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),qg=e=>Fk.has(e),zk=e=>Object.keys(e).some(qg),Hf=e=>e===Un||e===I,Kf=(e,t)=>parseFloat(e.split(", ")[t]),Gf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return Kf(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?Kf(o[1],e):0}},Bk=new Set(["x","y","z"]),Uk=Vi.filter(e=>!Bk.has(e));function $k(e){const t=[];return Uk.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const Pr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Gf(4,13),y:Gf(5,14)};Pr.translateX=Pr.x;Pr.translateY=Pr.y;const Wk=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,a={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{a[u]=Pr[u](r,o)}),t.render();const l=t.measureViewportBox();return n.forEach(u=>{const d=t.getValue(u);d&&d.jump(a[u]),e[u]=Pr[u](l,o)}),e},Hk=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(qg);let o=[],s=!1;const a=[];if(i.forEach(l=>{const u=e.getValue(l);if(!e.hasValue(l))return;let d=n[l],c=Fr(d);const f=t[l];let g;if(is(f)){const w=f.length,x=f[0]===null?1:0;d=f[x],c=Fr(d);for(let C=x;C<w&&f[C]!==null;C++)g?oc(Fr(f[C])===g):g=Fr(f[C])}else g=Fr(f);if(c!==g)if(Hf(c)&&Hf(g)){const w=u.get();typeof w=="string"&&u.set(parseFloat(w)),typeof f=="string"?t[l]=parseFloat(f):Array.isArray(f)&&g===I&&(t[l]=f.map(parseFloat))}else c!=null&&c.transform&&(g!=null&&g.transform)&&(d===0||f===0)?d===0?u.set(g.transform(d)):t[l]=c.transform(f):(s||(o=$k(e),s=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],u.jump(f))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,u=Wk(t,e,a);return o.length&&o.forEach(([d,c])=>{e.getValue(d).set(c)}),e.render(),_s&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function Kk(e,t,n,r){return zk(t)?Hk(e,t,n,r):{target:t,transitionEnd:r}}const Gk=(e,t,n,r)=>{const i=Ok(e,t,r);return t=i.target,r=i.transitionEnd,Kk(e,t,n,r)},Bl={current:null},Zg={current:!1};function Qk(){if(Zg.current=!0,!!_s)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Bl.current=e.matches;e.addListener(t),t()}else Bl.current=!1}function Yk(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(Be(o))e.addValue(i,o),ls(r)&&r.add(i);else if(Be(s))e.addValue(i,kr(o,{owner:e})),ls(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const a=e.getValue(i);!a.hasAnimated&&a.set(o)}else{const a=e.getStaticValue(i);e.addValue(i,kr(a!==void 0?a:o,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const Qf=new WeakMap,Jg=Object.keys(Ti),Xk=Jg.length,Yf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],qk=Xu.length;class Zk{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>q.render(this.render,!1,!0);const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=Ds(n),this.isVariantNode=Im(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...d}=this.scrapeMotionValuesFromProps(n,{});for(const c in d){const f=d[c];a[c]!==void 0&&Be(f)&&(f.set(a[c],!1),ls(u)&&u.add(c))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,Qf.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Zg.current||Qk(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Bl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Qf.delete(this.current),this.projection&&this.projection.unmount(),Lt(this.notifyUpdate),Lt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=Bn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&q.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,o){let s,a;for(let l=0;l<Xk;l++){const u=Jg[l],{isEnabled:d,Feature:c,ProjectionNode:f,MeasureLayout:g}=Ti[u];f&&(s=f),d(n)&&(!this.features[u]&&c&&(this.features[u]=new c(this)),g&&(a=g))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:d,dragConstraints:c,layoutScroll:f,layoutRoot:g}=n;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!d||c&&or(c),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:o,layoutScroll:f,layoutRoot:g})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):he()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Yf.length;r++){const i=Yf[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=t["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=Yk(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<qk;r++){const i=Xu[r],o=this.props[i];(Ei(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=kr(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=ic(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!Be(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new hc),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class ev extends Zk{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=mC(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){pC(this,r,s);const a=Gk(this,r,s,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function Jk(e){return window.getComputedStyle(e)}class eP extends ev{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(Bn.has(n)){const r=uc(n);return r&&r.default||0}else{const r=Jk(t),i=(Bm(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Bg(t,n)}build(t,n,r,i){Ju(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return rc(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Be(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){Gm(t,n,r,i)}}class tP extends ev{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(Bn.has(n)){const r=uc(n);return r&&r.default||0}return n=Qm.has(n)?n:Qu(n),t.getAttribute(n)}measureInstanceViewportBox(){return he()}scrapeMotionValuesFromProps(t,n){return Xm(t,n)}build(t,n,r,i){tc(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){Ym(t,n,r,i)}mount(t){this.isSVGTag=nc(t.tagName),super.mount(t)}}const nP=(e,t)=>Zu(e)?new tP(t,{enableHardwareAcceleration:!1}):new eP(t,{enableHardwareAcceleration:!0}),rP={layout:{ProjectionNode:Xg,MeasureLayout:$g}},iP={...RC,...Xw,...Dk,...rP},L=ow((e,t)=>Iw(e,t,iP,nP));function tv(){const e=y.useRef(!1);return Gu(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function oP(){const e=tv(),[t,n]=y.useState(0),r=y.useCallback(()=>{e.current&&n(t+1)},[t]);return[y.useCallback(()=>q.postRender(r),[r]),t]}class sP extends y.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function aP({children:e,isPresent:t}){const n=y.useId(),r=y.useRef(null),i=y.useRef({width:0,height:0,top:0,left:0});return y.useInsertionEffect(()=>{const{width:o,height:s,top:a,left:l}=i.current;if(t||!r.current||!o||!s)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${s}px !important;
            top: ${a}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),y.createElement(sP,{isPresent:t,childRef:r,sizeRef:i},y.cloneElement(e,{ref:r}))}const ja=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:o,mode:s})=>{const a=qm(lP),l=y.useId(),u=y.useMemo(()=>({id:l,initial:t,isPresent:n,custom:i,onExitComplete:d=>{a.set(d,!0);for(const c of a.values())if(!c)return;r&&r()},register:d=>(a.set(d,!1),()=>a.delete(d))}),o?void 0:[n]);return y.useMemo(()=>{a.forEach((d,c)=>a.set(c,!1))},[n]),y.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),s==="popLayout"&&(e=y.createElement(aP,{isPresent:n},e)),y.createElement(As.Provider,{value:u},e)};function lP(){return new Map}function uP(e){return y.useEffect(()=>()=>e(),[])}const Pn=e=>e.key||"";function cP(e,t){e.forEach(n=>{const r=Pn(n);t.set(r,n)})}function dP(e){const t=[];return y.Children.forEach(e,n=>{y.isValidElement(n)&&t.push(n)}),t}const fP=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:i,presenceAffectsLayout:o=!0,mode:s="sync"})=>{const a=y.useContext(qu).forceRender||oP()[0],l=tv(),u=dP(e);let d=u;const c=y.useRef(new Map).current,f=y.useRef(d),g=y.useRef(new Map).current,w=y.useRef(!0);if(Gu(()=>{w.current=!1,cP(u,g),f.current=d}),uP(()=>{w.current=!0,g.clear(),c.clear()}),w.current)return y.createElement(y.Fragment,null,d.map(p=>y.createElement(ja,{key:Pn(p),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:o,mode:s},p)));d=[...d];const x=f.current.map(Pn),C=u.map(Pn),v=x.length;for(let p=0;p<v;p++){const m=x[p];C.indexOf(m)===-1&&!c.has(m)&&c.set(m,void 0)}return s==="wait"&&c.size&&(d=[]),c.forEach((p,m)=>{if(C.indexOf(m)!==-1)return;const S=g.get(m);if(!S)return;const k=x.indexOf(m);let b=p;if(!b){const P=()=>{c.delete(m);const E=Array.from(g.keys()).filter(R=>!C.includes(R));if(E.forEach(R=>g.delete(R)),f.current=u.filter(R=>{const j=Pn(R);return j===m||E.includes(j)}),!c.size){if(l.current===!1)return;a(),r&&r()}};b=y.createElement(ja,{key:Pn(S),isPresent:!1,onExitComplete:P,custom:t,presenceAffectsLayout:o,mode:s},S),c.set(m,b)}d.splice(k,0,b)}),d=d.map(p=>{const m=p.key;return c.has(m)?p:y.createElement(ja,{key:Pn(p),isPresent:!0,presenceAffectsLayout:o,mode:s},p)}),y.createElement(y.Fragment,null,c.size?d:d.map(p=>y.cloneElement(p)))};var pP={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const hP=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),pe=(e,t)=>{const n=y.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,children:a,...l},u)=>y.createElement("svg",{ref:u,...pP,width:i,height:i,stroke:r,strokeWidth:s?Number(o)*24/Number(i):o,className:`lucide lucide-${hP(e)}`,...l},[...t.map(([d,c])=>y.createElement(d,c)),...(Array.isArray(a)?a:[a])||[]]));return n.displayName=`${e}`,n},Xf=pe("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),mP=pe("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),gP=pe("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),vP=pe("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),yP=pe("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),xP=pe("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),wP=pe("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]),ji=pe("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),SP=pe("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),nv=pe("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),CP=pe("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),kP=pe("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),PP=pe("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]),bP=pe("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),EP=pe("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),Ul=pe("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]),rv=pe("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]),Ra=pe("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),TP=pe("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),NP=pe("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),mc=pe("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function iv(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=iv(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function ov(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=iv(e))&&(r&&(r+=" "),r+=t);return r}function jP(){for(var e=0,t,n,r="";e<arguments.length;)(t=arguments[e++])&&(n=sv(t))&&(r&&(r+=" "),r+=n);return r}function sv(e){if(typeof e=="string")return e;for(var t,n="",r=0;r<e.length;r++)e[r]&&(t=sv(e[r]))&&(n&&(n+=" "),n+=t);return n}var gc="-";function RP(e){var t=AP(e),n=e.conflictingClassGroups,r=e.conflictingClassGroupModifiers,i=r===void 0?{}:r;function o(a){var l=a.split(gc);return l[0]===""&&l.length!==1&&l.shift(),av(l,t)||MP(a)}function s(a,l){var u=n[a]||[];return l&&i[a]?[].concat(u,i[a]):u}return{getClassGroupId:o,getConflictingClassGroupIds:s}}function av(e,t){var s;if(e.length===0)return t.classGroupId;var n=e[0],r=t.nextPart.get(n),i=r?av(e.slice(1),r):void 0;if(i)return i;if(t.validators.length!==0){var o=e.join(gc);return(s=t.validators.find(function(a){var l=a.validator;return l(o)}))==null?void 0:s.classGroupId}}var qf=/^\[(.+)\]$/;function MP(e){if(qf.test(e)){var t=qf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}function AP(e){var t=e.theme,n=e.prefix,r={nextPart:new Map,validators:[]},i=LP(Object.entries(e.classGroups),n);return i.forEach(function(o){var s=o[0],a=o[1];$l(a,r,s,t)}),r}function $l(e,t,n,r){e.forEach(function(i){if(typeof i=="string"){var o=i===""?t:Zf(t,i);o.classGroupId=n;return}if(typeof i=="function"){if(_P(i)){$l(i(r),t,n,r);return}t.validators.push({validator:i,classGroupId:n});return}Object.entries(i).forEach(function(s){var a=s[0],l=s[1];$l(l,Zf(t,a),n,r)})})}function Zf(e,t){var n=e;return t.split(gc).forEach(function(r){n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function _P(e){return e.isThemeGetter}function LP(e,t){return t?e.map(function(n){var r=n[0],i=n[1],o=i.map(function(s){return typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(function(a){var l=a[0],u=a[1];return[t+l,u]})):s});return[r,o]}):e}function DP(e){if(e<1)return{get:function(){},set:function(){}};var t=0,n=new Map,r=new Map;function i(o,s){n.set(o,s),t++,t>e&&(t=0,r=n,n=new Map)}return{get:function(s){var a=n.get(s);if(a!==void 0)return a;if((a=r.get(s))!==void 0)return i(s,a),a},set:function(s,a){n.has(s)?n.set(s,a):i(s,a)}}}var lv="!";function VP(e){var t=e.separator||":",n=t.length===1,r=t[0],i=t.length;return function(s){for(var a=[],l=0,u=0,d,c=0;c<s.length;c++){var f=s[c];if(l===0){if(f===r&&(n||s.slice(c,c+i)===t)){a.push(s.slice(u,c)),u=c+i;continue}if(f==="/"){d=c;continue}}f==="["?l++:f==="]"&&l--}var g=a.length===0?s:s.substring(u),w=g.startsWith(lv),x=w?g.substring(1):g,C=d&&d>u?d-u:void 0;return{modifiers:a,hasImportantModifier:w,baseClassName:x,maybePostfixModifierPosition:C}}}function IP(e){if(e.length<=1)return e;var t=[],n=[];return e.forEach(function(r){var i=r[0]==="[";i?(t.push.apply(t,n.sort().concat([r])),n=[]):n.push(r)}),t.push.apply(t,n.sort()),t}function OP(e){return{cache:DP(e.cacheSize),splitModifiers:VP(e),...RP(e)}}var FP=/\s+/;function zP(e,t){var n=t.splitModifiers,r=t.getClassGroupId,i=t.getConflictingClassGroupIds,o=new Set;return e.trim().split(FP).map(function(s){var a=n(s),l=a.modifiers,u=a.hasImportantModifier,d=a.baseClassName,c=a.maybePostfixModifierPosition,f=r(c?d.substring(0,c):d),g=!!c;if(!f){if(!c)return{isTailwindClass:!1,originalClassName:s};if(f=r(d),!f)return{isTailwindClass:!1,originalClassName:s};g=!1}var w=IP(l).join(":"),x=u?w+lv:w;return{isTailwindClass:!0,modifierId:x,classGroupId:f,originalClassName:s,hasPostfixModifier:g}}).reverse().filter(function(s){if(!s.isTailwindClass)return!0;var a=s.modifierId,l=s.classGroupId,u=s.hasPostfixModifier,d=a+l;return o.has(d)?!1:(o.add(d),i(l,u).forEach(function(c){return o.add(a+c)}),!0)}).reverse().map(function(s){return s.originalClassName}).join(" ")}function BP(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i,o,s=a;function a(u){var d=t[0],c=t.slice(1),f=c.reduce(function(g,w){return w(g)},d());return r=OP(f),i=r.cache.get,o=r.cache.set,s=l,l(u)}function l(u){var d=i(u);if(d)return d;var c=zP(u,r);return o(u,c),c}return function(){return s(jP.apply(null,arguments))}}function Z(e){var t=function(r){return r[e]||[]};return t.isThemeGetter=!0,t}var uv=/^\[(?:([a-z-]+):)?(.+)\]$/i,UP=/^\d+\/\d+$/,$P=new Set(["px","full","screen"]),WP=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,HP=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,KP=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function lt(e){return jn(e)||$P.has(e)||UP.test(e)||Wl(e)}function Wl(e){return $n(e,"length",ZP)}function GP(e){return $n(e,"size",cv)}function QP(e){return $n(e,"position",cv)}function YP(e){return $n(e,"url",JP)}function co(e){return $n(e,"number",jn)}function jn(e){return!Number.isNaN(Number(e))}function XP(e){return e.endsWith("%")&&jn(e.slice(0,-1))}function Br(e){return Jf(e)||$n(e,"number",Jf)}function U(e){return uv.test(e)}function Ur(){return!0}function It(e){return WP.test(e)}function qP(e){return $n(e,"",eb)}function $n(e,t,n){var r=uv.exec(e);return r?r[1]?r[1]===t:n(r[2]):!1}function ZP(e){return HP.test(e)}function cv(){return!1}function JP(e){return e.startsWith("url(")}function Jf(e){return Number.isInteger(Number(e))}function eb(e){return KP.test(e)}function tb(){var e=Z("colors"),t=Z("spacing"),n=Z("blur"),r=Z("brightness"),i=Z("borderColor"),o=Z("borderRadius"),s=Z("borderSpacing"),a=Z("borderWidth"),l=Z("contrast"),u=Z("grayscale"),d=Z("hueRotate"),c=Z("invert"),f=Z("gap"),g=Z("gradientColorStops"),w=Z("gradientColorStopPositions"),x=Z("inset"),C=Z("margin"),v=Z("opacity"),p=Z("padding"),m=Z("saturate"),S=Z("scale"),k=Z("sepia"),b=Z("skew"),P=Z("space"),E=Z("translate"),R=function(){return["auto","contain","none"]},j=function(){return["auto","hidden","clip","visible","scroll"]},O=function(){return["auto",U,t]},D=function(){return[U,t]},H=function(){return["",lt]},A=function(){return["auto",jn,U]},G=function(){return["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"]},z=function(){return["solid","dashed","dotted","double","none"]},Q=function(){return["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]},N=function(){return["start","end","center","between","around","evenly","stretch"]},_=function(){return["","0",U]},F=function(){return["auto","avoid","all","avoid-page","page","left","right","column"]},V=function(){return[jn,co]},$=function(){return[jn,U]};return{cacheSize:500,theme:{colors:[Ur],spacing:[lt],blur:["none","",It,U],brightness:V(),borderColor:[e],borderRadius:["none","","full",It,U],borderSpacing:D(),borderWidth:H(),contrast:V(),grayscale:_(),hueRotate:$(),invert:_(),gap:D(),gradientColorStops:[e],gradientColorStopPositions:[XP,Wl],inset:O(),margin:O(),opacity:V(),padding:D(),saturate:V(),scale:V(),sepia:_(),skew:$(),space:D(),translate:D()},classGroups:{aspect:[{aspect:["auto","square","video",U]}],container:["container"],columns:[{columns:[It]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(G(),[U])}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:R()}],"overscroll-x":[{"overscroll-x":R()}],"overscroll-y":[{"overscroll-y":R()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[x]}],"inset-x":[{"inset-x":[x]}],"inset-y":[{"inset-y":[x]}],start:[{start:[x]}],end:[{end:[x]}],top:[{top:[x]}],right:[{right:[x]}],bottom:[{bottom:[x]}],left:[{left:[x]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Br]}],basis:[{basis:O()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",U]}],grow:[{grow:_()}],shrink:[{shrink:_()}],order:[{order:["first","last","none",Br]}],"grid-cols":[{"grid-cols":[Ur]}],"col-start-end":[{col:["auto",{span:["full",Br]},U]}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":[Ur]}],"row-start-end":[{row:["auto",{span:[Br]},U]}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",U]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",U]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal"].concat(N())}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(N(),["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(N(),["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[C]}],mx:[{mx:[C]}],my:[{my:[C]}],ms:[{ms:[C]}],me:[{me:[C]}],mt:[{mt:[C]}],mr:[{mr:[C]}],mb:[{mb:[C]}],ml:[{ml:[C]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",U,t]}],"min-w":[{"min-w":["min","max","fit",U,lt]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[It]},It,U]}],h:[{h:[U,t,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",U,lt]}],"max-h":[{"max-h":[U,t,"min","max","fit"]}],"font-size":[{text:["base",It,Wl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",co]}],"font-family":[{font:[Ur]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",U]}],"line-clamp":[{"line-clamp":["none",jn,co]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",U,lt]}],"list-image":[{"list-image":["none",U]}],"list-style-type":[{list:["none","disc","decimal",U]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(z(),["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",lt]}],"underline-offset":[{"underline-offset":["auto",U,lt]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:D()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",U]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",U]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(G(),[QP])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",GP]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},YP]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[g]}],"gradient-via":[{via:[g]}],"gradient-to":[{to:[g]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[].concat(z(),["hidden"])}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:z()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:[""].concat(z())}],"outline-offset":[{"outline-offset":[U,lt]}],"outline-w":[{outline:[lt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:H()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[lt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",It,qP]}],"shadow-color":[{shadow:[Ur]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":Q()}],"bg-blend":[{"bg-blend":Q()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",It,U]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[c]}],saturate:[{saturate:[m]}],sepia:[{sepia:[k]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[m]}],"backdrop-sepia":[{"backdrop-sepia":[k]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",U]}],duration:[{duration:$()}],ease:[{ease:["linear","in","out","in-out",U]}],delay:[{delay:$()}],animate:[{animate:["none","spin","ping","pulse","bounce",U]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[Br,U]}],"translate-x":[{"translate-x":[E]}],"translate-y":[{"translate-y":[E]}],"skew-x":[{"skew-x":[b]}],"skew-y":[{"skew-y":[b]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",U]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",U]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":D()}],"scroll-mx":[{"scroll-mx":D()}],"scroll-my":[{"scroll-my":D()}],"scroll-ms":[{"scroll-ms":D()}],"scroll-me":[{"scroll-me":D()}],"scroll-mt":[{"scroll-mt":D()}],"scroll-mr":[{"scroll-mr":D()}],"scroll-mb":[{"scroll-mb":D()}],"scroll-ml":[{"scroll-ml":D()}],"scroll-p":[{"scroll-p":D()}],"scroll-px":[{"scroll-px":D()}],"scroll-py":[{"scroll-py":D()}],"scroll-ps":[{"scroll-ps":D()}],"scroll-pe":[{"scroll-pe":D()}],"scroll-pt":[{"scroll-pt":D()}],"scroll-pr":[{"scroll-pr":D()}],"scroll-pb":[{"scroll-pb":D()}],"scroll-pl":[{"scroll-pl":D()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",U]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[lt,co]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}var nb=BP(tb);function Ye(...e){return nb(ov(e))}function ep(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function rb(...e){return t=>{let n=!1;const r=e.map(i=>{const o=ep(i,t);return!n&&typeof o=="function"&&(n=!0),o});if(n)return()=>{for(let i=0;i<r.length;i++){const o=r[i];typeof o=="function"?o():ep(e[i],null)}}}}function je(...e){return y.useCallback(rb(...e),e)}function cs(e){const t=ob(e),n=y.forwardRef((r,i)=>{const{children:o,...s}=r,a=y.Children.toArray(o),l=a.find(ab);if(l){const u=l.props.children,d=a.map(c=>c===l?y.Children.count(u)>1?y.Children.only(null):y.isValidElement(u)?u.props.children:null:c);return h.jsx(t,{...s,ref:i,children:y.isValidElement(u)?y.cloneElement(u,void 0,d):null})}return h.jsx(t,{...s,ref:i,children:o})});return n.displayName=`${e}.Slot`,n}var ib=cs("Slot");function ob(e){const t=y.forwardRef((n,r)=>{const{children:i,...o}=n,s=y.isValidElement(i)?ub(i):void 0,a=je(s,r);if(y.isValidElement(i)){const l=lb(o,i.props);return i.type!==y.Fragment&&(l.ref=a),y.cloneElement(i,l)}return y.Children.count(i)>1?y.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var sb=Symbol("radix.slottable");function ab(e){return y.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===sb}function lb(e,t){const n={...t};for(const r in t){const i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...a)=>{const l=o(...a);return i(...a),l}:i&&(n[r]=i):r==="style"?n[r]={...i,...o}:r==="className"&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}function ub(e){var r,i;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(i=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:i.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}const tp=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,np=ov,vc=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return np(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:i,defaultVariants:o}=t,s=Object.keys(i).map(u=>{const d=n==null?void 0:n[u],c=o==null?void 0:o[u];if(d===null)return null;const f=tp(d)||tp(c);return i[u][f]}),a=n&&Object.entries(n).reduce((u,d)=>{let[c,f]=d;return f===void 0||(u[c]=f),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,d)=>{let{class:c,className:f,...g}=d;return Object.entries(g).every(w=>{let[x,C]=w;return Array.isArray(C)?C.includes({...o,...a}[x]):{...o,...a}[x]===C})?[...u,c,f]:u},[]);return np(e,s,l,n==null?void 0:n.class,n==null?void 0:n.className)},cb=vc("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),ce=re.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...i},o)=>{const s=r?ib:"button";return h.jsx(s,{className:Ye(cb({variant:t,size:n,className:e})),ref:o,...i})});ce.displayName="Button";const dv="/assets/C-Cube-Logo-f839ec34.png",fb=()=>{const[e,t]=y.useState(!1),n=[{name:"Home",path:"/"},{name:"Shop",path:"/shop"},{name:"About",path:"/about"},{name:"Contact",path:"/contact"}],r={hover:{scale:1.1,color:"#F2BED1",transition:{duration:.2}},tap:{scale:.95}},i={open:{opacity:1,y:0,transition:{type:"spring",stiffness:300,damping:24}},closed:{opacity:0,y:20,transition:{duration:.2}}};return h.jsxs(L.nav,{initial:{y:-100},animate:{y:0},transition:{duration:.5,ease:"easeOut"},className:"bg-pastel-light/80 backdrop-blur-md shadow-md sticky top-0 z-50",children:[h.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:h.jsxs("div",{className:"flex items-center justify-between h-20",children:[h.jsxs(nt,{to:"/",className:"flex items-center space-x-2",children:[h.jsx("img",{src:dv,alt:"C Cube Logo",className:"h-20 w-20 text-pastel-accent"}),h.jsx("span",{className:"font-heading text-2xl font-bold text-pastel-accent",children:"C Cube"})]}),h.jsx("div",{className:"hidden md:flex items-center space-x-6",children:n.map(o=>h.jsx(L.div,{variants:r,whileHover:"hover",whileTap:"tap",children:h.jsx($d,{to:o.path,className:({isActive:s})=>`text-pastel-accent hover:text-pastel-dark transition-colors duration-300 pb-1 ${s?"font-semibold border-b-2 border-pastel-dark":""}`,children:o.name})},o.name))}),h.jsx("div",{className:"hidden md:flex items-center",children:h.jsx(ce,{variant:"ghost",size:"icon",className:"text-pastel-accent hover:text-pastel-dark hover:bg-pastel-medium/50",children:h.jsx(Ul,{size:24})})}),h.jsxs("div",{className:"md:hidden flex items-center",children:[h.jsx(ce,{variant:"ghost",size:"icon",className:"text-pastel-accent hover:text-pastel-dark hover:bg-pastel-medium/50 mr-2",children:h.jsx(Ul,{size:24})}),h.jsx(ce,{variant:"ghost",size:"icon",onClick:()=>t(!e),className:"text-pastel-accent hover:text-pastel-dark hover:bg-pastel-medium/50","aria-label":"Toggle menu",children:e?h.jsx(mc,{size:28}):h.jsx(kP,{size:28})})]})]})}),h.jsx(L.div,{initial:!1,animate:e?"open":"closed",variants:{open:{opacity:1,height:"auto",transition:{staggerChildren:.05,delayChildren:.2}},closed:{opacity:0,height:0,transition:{staggerChildren:.05,staggerDirection:-1}}},className:"md:hidden bg-pastel-light/95 backdrop-blur-sm",children:h.jsx("div",{className:"px-4 pt-2 pb-4 space-y-1 sm:px-6",children:n.map(o=>h.jsx(L.div,{variants:i,children:h.jsx($d,{to:o.path,onClick:()=>t(!1),className:({isActive:s})=>`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-300 ${s?"bg-pastel-medium text-pastel-accent font-semibold":"text-pastel-accent hover:bg-pastel-dark/30 hover:text-pastel-accent"}`,children:o.name})},o.name))})})]})},pb=()=>{const e=[{icon:h.jsx(SP,{size:24}),href:"https://instagram.com",label:"Instagram"},{icon:h.jsx(yP,{size:24}),href:"https://facebook.com",label:"Facebook"},{icon:h.jsx(TP,{size:24}),href:"https://twitter.com",label:"Twitter"}],t={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};return h.jsx(L.footer,{initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},transition:{staggerChildren:.2},className:"bg-pastel-medium text-pastel-accent py-12",children:h.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[h.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 items-center",children:[h.jsxs(L.div,{variants:t,className:"flex flex-col items-center md:items-start",children:[h.jsxs(nt,{to:"/",className:"flex items-center space-x-2 mb-4",children:[h.jsx("img",{src:dv,alt:"C Cube Logo",className:"h-20 w-20 text-pastel-accent"}),h.jsx("span",{className:"font-heading text-2xl font-bold",children:"C³ – C Cube"})]}),h.jsx("p",{className:"text-sm text-center md:text-left",children:"Handmade with heart – across six sides."})]}),h.jsxs(L.div,{variants:t,className:"flex flex-col items-center",children:[h.jsx("span",{className:"font-semibold mb-4 text-lg",children:"Quick Links"}),h.jsxs("ul",{className:"space-y-2 text-center",children:[h.jsx("li",{children:h.jsx(nt,{to:"/shop",className:"hover:text-pastel-bg transition-colors",children:"Shop All"})}),h.jsx("li",{children:h.jsx(nt,{to:"/about",className:"hover:text-pastel-bg transition-colors",children:"Our Story"})}),h.jsx("li",{children:h.jsx(nt,{to:"/contact",className:"hover:text-pastel-bg transition-colors",children:"Contact Us"})}),h.jsx("li",{children:h.jsx(nt,{to:"/contact#custom-orders",className:"hover:text-pastel-bg transition-colors",children:"Custom Orders"})})]})]}),h.jsxs(L.div,{variants:t,className:"flex flex-col items-center md:items-end",children:[h.jsx("span",{className:"font-semibold mb-4 text-lg",children:"Connect With Us"}),h.jsx("div",{className:"flex space-x-4 mb-4",children:e.map((n,r)=>h.jsx(L.a,{href:n.href,target:"_blank",rel:"noopener noreferrer","aria-label":n.label,whileHover:{scale:1.2,color:"#F9F5F6"},whileTap:{scale:.9},className:"text-pastel-accent hover:text-pastel-bg transition-colors",children:n.icon},r))}),h.jsxs("a",{href:"mailto:<EMAIL>",className:"flex items-center space-x-2 hover:text-pastel-bg transition-colors",children:[h.jsx(nv,{size:20}),h.jsx("span",{children:"<EMAIL>"})]})]})]}),h.jsx(L.div,{variants:t,className:"mt-10 pt-8 border-t border-pastel-dark/50 text-center text-sm",children:h.jsxs("p",{children:["© ",new Date().getFullYear()," C³ – C Cube. All rights reserved. Crafted with care in Hostinger Horizons."]})})]})})},hb=1;let Ma=0;function mb(){return Ma=(Ma+1)%Number.MAX_VALUE,Ma.toString()}const Me={state:{toasts:[]},listeners:[],getState:()=>Me.state,setState:e=>{typeof e=="function"?Me.state=e(Me.state):Me.state={...Me.state,...e},Me.listeners.forEach(t=>t(Me.state))},subscribe:e=>(Me.listeners.push(e),()=>{Me.listeners=Me.listeners.filter(t=>t!==e)})},gb=({...e})=>{const t=mb(),n=i=>Me.setState(o=>({...o,toasts:o.toasts.map(s=>s.id===t?{...s,...i}:s)})),r=()=>Me.setState(i=>({...i,toasts:i.toasts.filter(o=>o.id!==t)}));return Me.setState(i=>({...i,toasts:[{...e,id:t,dismiss:r},...i.toasts].slice(0,hb)})),{id:t,dismiss:r,update:n}};function zi(){const[e,t]=y.useState(Me.getState());return y.useEffect(()=>Me.subscribe(r=>{t(r)}),[]),y.useEffect(()=>{const n=[];return e.toasts.forEach(r=>{if(r.duration===1/0)return;const i=setTimeout(()=>{r.dismiss()},r.duration||5e3);n.push(i)}),()=>{n.forEach(r=>clearTimeout(r))}},[e.toasts]),{toast:gb,toasts:e.toasts}}const yc=({product:e})=>{const{toast:t}=zi(),n=o=>{o.preventDefault(),o.stopPropagation(),t({title:"💖 Added to Cart!",description:`${e.name} is now in your cart.`,duration:3e3}),console.log(`Added ${e.name} to cart.`)},r={rest:{scale:1,boxShadow:"0px 5px 10px rgba(0,0,0,0.1)"},hover:{scale:1.03,boxShadow:"0px 10px 20px rgba(46, 71, 74, 0.15)",transition:{duration:.3,ease:"easeOut"}}},i={rest:{scale:1},hover:{scale:1.1,transition:{duration:.3,ease:"easeOut"}}};return h.jsx(L.div,{variants:r,initial:"rest",whileHover:"hover",animate:"rest",className:"bg-white rounded-xl overflow-hidden shadow-lg group cursor-pointer flex flex-col h-full",children:h.jsxs(nt,{to:`/shop/${e.id}`,className:"block flex flex-col h-full",children:[h.jsxs("div",{className:"relative overflow-hidden aspect-[4/3]",children:[h.jsx(L.div,{variants:i,className:"w-full h-full",children:h.jsx("img",{class:"w-full h-full object-cover",alt:e.name,src:"https://images.unsplash.com/photo-1671376354106-d8d21e55dddd"})}),h.jsx("div",{className:"absolute top-2 right-2",children:h.jsx(ce,{variant:"ghost",size:"icon",className:"bg-white/70 hover:bg-pastel-medium text-pastel-accent rounded-full backdrop-blur-sm",onClick:o=>{o.preventDefault(),o.stopPropagation(),t({title:"❤️ Added to Wishlist!",description:`${e.name} has been added to your wishlist.`,duration:2e3})},"aria-label":"Add to wishlist",children:h.jsx(ji,{size:20})})})]}),h.jsxs("div",{className:"p-5 flex flex-col flex-grow",children:[h.jsx("span",{className:"text-xs text-pastel-dark font-medium uppercase tracking-wider mb-1",children:e.category}),h.jsx("h3",{className:"text-lg font-semibold text-pastel-accent mb-2 group-hover:text-pastel-dark transition-colors duration-300 truncate",children:e.name}),h.jsx("p",{className:"text-sm text-pastel-accent/70 mb-3 flex-grow line-clamp-2",children:e.description}),h.jsxs("div",{className:"mt-auto",children:[h.jsxs("p",{className:"text-xl font-bold text-pastel-accent mb-4",children:["$",e.price]}),h.jsxs(ce,{onClick:n,className:"w-full bg-pastel-dark text-white hover:bg-pastel-accent transition-colors duration-300 group-hover:scale-105 transform","aria-label":`Add ${e.name} to cart`,children:["Add to Cart ",h.jsx(rv,{size:18,className:"ml-2"})]})]})]})]})})},vb=[{id:1,name:"Pastel Crochet Bag",category:"Crochet",price:"25.00",imageSrc:"crochet_bag_pastel",description:"A charming handmade crochet bag in soft pastel hues."},{id:2,name:"Scented Soy Candle",category:"Candles",price:"15.00",imageSrc:"scented_candle_pastel",description:"Relaxing scented soy candle, perfect for cozy evenings."},{id:3,name:"Clay Trinket Dish",category:"Clay",price:"12.00",imageSrc:"clay_trinket_dish",description:"Cute clay dish for your tiny treasures."},{id:4,name:"Mini Canvas Art",category:"Canvas",price:"30.00",imageSrc:"mini_canvas_art_abstract",description:"Unique abstract mini canvas art to brighten your space."}],yb=()=>{const{toast:e}=zi(),t={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2,delayChildren:.3}}},n={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:100}}},r={initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1,transition:{duration:.8,ease:"easeOut"}}},i=()=>{e({title:"🛍️ Off to Shopping!",description:"Get ready to find your new favorite handmade treasures!",duration:3e3})};return h.jsxs(L.div,{initial:"hidden",animate:"visible",variants:t,className:"animate-fade-in",children:[h.jsxs(L.section,{variants:r,initial:"initial",animate:"animate",className:"bg-gradient-to-br from-pastel-light via-pastel-medium to-pastel-dark py-20 md:py-32 text-center relative overflow-hidden",children:[h.jsx("div",{className:"absolute inset-0 opacity-30",children:h.jsx("img",{class:"w-full h-full object-cover",alt:"Abstract pastel background texture",src:"https://images.unsplash.com/photo-1627052046046-c506756f1ab2"})}),h.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[h.jsxs(L.h1,{variants:n,className:"text-4xl sm:text-5xl md:text-6xl font-bold text-pastel-accent mb-6",children:["C³ – ",h.jsx("span",{className:"text-white",children:"C Cube"})]}),h.jsx(L.p,{variants:n,className:"text-xl md:text-2xl text-pastel-accent/80 mb-10",children:"Handmade with heart – across six sides."}),h.jsx(L.div,{variants:n,children:h.jsx(ce,{asChild:!0,size:"lg",className:"bg-pastel-accent text-pastel-bg hover:bg-pastel-accent/90 shadow-lg transform hover:scale-105 transition-transform duration-300 group",children:h.jsxs(nt,{to:"/shop",onClick:i,children:["Shop Our Collections ",h.jsx(Ul,{size:20,className:"ml-2 group-hover:animate-bounce"})]})})})]})]}),h.jsx("section",{className:"py-16 md:py-24 bg-pastel-bg",children:h.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[h.jsx(L.h2,{variants:n,className:"text-3xl md:text-4xl font-bold text-pastel-accent text-center mb-12",children:"Featured Treasures"}),h.jsx(L.div,{variants:t,className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8",children:vb.map((o,s)=>h.jsx(L.div,{variants:n,custom:s,className:"animate-slide-in-up",style:{animationDelay:`${s*.1}s`},children:h.jsx(yc,{product:o})},o.id))}),h.jsx(L.div,{variants:n,className:"text-center mt-12",children:h.jsx(ce,{asChild:!0,variant:"outline",size:"lg",className:"border-pastel-dark text-pastel-accent hover:bg-pastel-dark hover:text-pastel-bg shadow-md",children:h.jsxs(nt,{to:"/shop",children:["Explore All Products ",h.jsx(ji,{size:18,className:"ml-2 fill-current text-pastel-dark group-hover:text-pastel-bg"})]})})})]})}),h.jsx("section",{className:"py-16 md:py-24 bg-pastel-light",children:h.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[h.jsx(L.h2,{variants:n,className:"text-3xl md:text-4xl font-bold text-pastel-accent text-center mb-12",children:"Discover Our Crafts"}),h.jsx(L.div,{variants:t,className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 text-center",children:["Crochet","Candles","Crafts","Clay","Concrete","Canvas"].map((o,s)=>h.jsx(L.div,{variants:n,custom:s,className:"animate-slide-in-up bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 transform hover:-translate-y-1 cursor-pointer group",style:{animationDelay:`${s*.08}s`},children:h.jsxs(nt,{to:`/shop?category=${o.toLowerCase()}`,className:"block",children:[h.jsx("div",{className:"w-20 h-20 mx-auto bg-pastel-medium rounded-full flex items-center justify-center mb-4 group-hover:bg-pastel-dark transition-colors duration-300",children:h.jsx(wP,{size:36,className:"text-pastel-accent group-hover:text-white transition-colors duration-300"})}),h.jsx("h3",{className:"text-xl font-semibold text-pastel-accent group-hover:text-pastel-dark transition-colors duration-300",children:o})]})},o))})]})}),h.jsx("section",{className:"py-16 md:py-24 bg-pastel-bg",children:h.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[h.jsx(L.h2,{variants:n,className:"text-3xl md:text-4xl font-bold text-pastel-accent text-center mb-12",children:"Follow Our Journey @c_cube_eg"}),h.jsx(L.div,{variants:t,className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4",children:[1,2,3,4].map(o=>h.jsxs(L.div,{variants:n,custom:o,className:"animate-slide-in-up aspect-square bg-pastel-medium rounded-lg shadow-md overflow-hidden group relative",style:{animationDelay:`${o*.1}s`},children:[h.jsx("img",{class:"w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-300",alt:`Instagram post placeholder ${o}`,src:"https://images.unsplash.com/photo-1680504195875-2fe0b4ec2fd0"}),h.jsx("div",{className:"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center",children:h.jsx(ji,{size:32,className:"text-white"})})]},o))}),h.jsx(L.div,{variants:n,className:"text-center mt-12",children:h.jsx(ce,{variant:"outline",size:"lg",className:"border-pastel-dark text-pastel-accent hover:bg-pastel-dark hover:text-pastel-bg shadow-md",onClick:()=>window.open("https://instagram.com/c_cube_eg","_blank"),children:"View on Instagram"})})]})}),h.jsx("section",{className:"py-16 md:py-24 bg-gradient-to-tl from-pastel-light via-pastel-medium to-pastel-dark",children:h.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[h.jsx(L.h2,{variants:n,className:"text-3xl md:text-4xl font-bold text-pastel-accent mb-6",children:"Have a Special Request?"}),h.jsx(L.p,{variants:n,className:"text-lg text-pastel-accent/80 mb-8 max-w-2xl mx-auto",children:"We love creating unique pieces! If you have a custom order in mind, reach out to us, and let's bring your vision to life."}),h.jsx(L.div,{variants:n,children:h.jsx(ce,{asChild:!0,size:"lg",className:"bg-pastel-accent text-pastel-bg hover:bg-pastel-accent/90 shadow-lg transform hover:scale-105 transition-transform duration-300",children:h.jsx(nt,{to:"/contact#custom-orders",children:"Request a Custom Order"})})})]})})]})};function Fs(e,t=[]){let n=[];function r(o,s){const a=y.createContext(s),l=n.length;n=[...n,s];const u=c=>{var v;const{scope:f,children:g,...w}=c,x=((v=f==null?void 0:f[e])==null?void 0:v[l])||a,C=y.useMemo(()=>w,Object.values(w));return h.jsx(x.Provider,{value:C,children:g})};u.displayName=o+"Provider";function d(c,f){var x;const g=((x=f==null?void 0:f[e])==null?void 0:x[l])||a,w=y.useContext(g);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${c}\` must be used within \`${o}\``)}return[u,d]}const i=()=>{const o=n.map(s=>y.createContext(s));return function(a){const l=(a==null?void 0:a[e])||o;return y.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return i.scopeName=e,[r,xb(i,...t)]}function xb(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(i=>({useScope:i(),scopeName:i.scopeName}));return function(o){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const c=l(o)[`__scope${u}`];return{...a,...c}},{});return y.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function fe(e,t,{checkForDefaultPrevented:n=!0}={}){return function(i){if(e==null||e(i),n===!1||!i.defaultPrevented)return t==null?void 0:t(i)}}var br=globalThis!=null&&globalThis.document?y.useLayoutEffect:()=>{},wb=xp[" useInsertionEffect ".trim().toString()]||br;function xc({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[i,o,s]=Sb({defaultProp:t,onChange:n}),a=e!==void 0,l=a?e:i;{const d=y.useRef(e!==void 0);y.useEffect(()=>{const c=d.current;c!==a&&console.warn(`${r} is changing from ${c?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),d.current=a},[a,r])}const u=y.useCallback(d=>{var c;if(a){const f=Cb(d)?d(e):d;f!==e&&((c=s.current)==null||c.call(s,f))}else o(d)},[a,e,o,s]);return[l,u]}function Sb({defaultProp:e,onChange:t}){const[n,r]=y.useState(e),i=y.useRef(n),o=y.useRef(t);return wb(()=>{o.current=t},[t]),y.useEffect(()=>{var s;i.current!==n&&((s=o.current)==null||s.call(o,n),i.current=n)},[n,i]),[n,r,o]}function Cb(e){return typeof e=="function"}function fv(e){const t=y.useRef({value:e,previous:e});return y.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function pv(e){const[t,n]=y.useState(void 0);return br(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(i=>{if(!Array.isArray(i)||!i.length)return;const o=i[0];let s,a;if("borderBoxSize"in o){const l=o.borderBoxSize,u=Array.isArray(l)?l[0]:l;s=u.inlineSize,a=u.blockSize}else s=e.offsetWidth,a=e.offsetHeight;n({width:s,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}function kb(e,t){return y.useReducer((n,r)=>t[n][r]??n,e)}var wc=e=>{const{present:t,children:n}=e,r=Pb(t),i=typeof n=="function"?n({present:r.isPresent}):y.Children.only(n),o=je(r.ref,bb(i));return typeof n=="function"||r.isPresent?y.cloneElement(i,{ref:o}):null};wc.displayName="Presence";function Pb(e){const[t,n]=y.useState(),r=y.useRef(null),i=y.useRef(e),o=y.useRef("none"),s=e?"mounted":"unmounted",[a,l]=kb(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return y.useEffect(()=>{const u=fo(r.current);o.current=a==="mounted"?u:"none"},[a]),br(()=>{const u=r.current,d=i.current;if(d!==e){const f=o.current,g=fo(u);e?l("MOUNT"):g==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&f!==g?"ANIMATION_OUT":"UNMOUNT"),i.current=e}},[e,l]),br(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,c=g=>{const x=fo(r.current).includes(g.animationName);if(g.target===t&&x&&(l("ANIMATION_END"),!i.current)){const C=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=C)})}},f=g=>{g.target===t&&(o.current=fo(r.current))};return t.addEventListener("animationstart",f),t.addEventListener("animationcancel",c),t.addEventListener("animationend",c),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",f),t.removeEventListener("animationcancel",c),t.removeEventListener("animationend",c)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:y.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function fo(e){return(e==null?void 0:e.animationName)||"none"}function bb(e){var r,i;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(i=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:i.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Eb=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],ye=Eb.reduce((e,t)=>{const n=cs(`Primitive.${t}`),r=y.forwardRef((i,o)=>{const{asChild:s,...a}=i,l=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),h.jsx(l,{...a,ref:o})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function hv(e,t){e&&Ts.flushSync(()=>e.dispatchEvent(t))}var zs="Checkbox",[Tb,GE]=Fs(zs),[Nb,Sc]=Tb(zs);function jb(e){const{__scopeCheckbox:t,checked:n,children:r,defaultChecked:i,disabled:o,form:s,name:a,onCheckedChange:l,required:u,value:d="on",internal_do_not_use_render:c}=e,[f,g]=xc({prop:n,defaultProp:i??!1,onChange:l,caller:zs}),[w,x]=y.useState(null),[C,v]=y.useState(null),p=y.useRef(!1),m=w?!!s||!!w.closest("form"):!0,S={checked:f,disabled:o,setChecked:g,control:w,setControl:x,name:a,form:s,value:d,hasConsumerStoppedPropagationRef:p,required:u,defaultChecked:sn(i)?!1:i,isFormControl:m,bubbleInput:C,setBubbleInput:v};return h.jsx(Nb,{scope:t,...S,children:Rb(c)?c(S):r})}var mv="CheckboxTrigger",gv=y.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...r},i)=>{const{control:o,value:s,disabled:a,checked:l,required:u,setControl:d,setChecked:c,hasConsumerStoppedPropagationRef:f,isFormControl:g,bubbleInput:w}=Sc(mv,e),x=je(i,d),C=y.useRef(l);return y.useEffect(()=>{const v=o==null?void 0:o.form;if(v){const p=()=>c(C.current);return v.addEventListener("reset",p),()=>v.removeEventListener("reset",p)}},[o,c]),h.jsx(ye.button,{type:"button",role:"checkbox","aria-checked":sn(l)?"mixed":l,"aria-required":u,"data-state":Sv(l),"data-disabled":a?"":void 0,disabled:a,value:s,...r,ref:x,onKeyDown:fe(t,v=>{v.key==="Enter"&&v.preventDefault()}),onClick:fe(n,v=>{c(p=>sn(p)?!0:!p),w&&g&&(f.current=v.isPropagationStopped(),f.current||v.stopPropagation())})})});gv.displayName=mv;var Cc=y.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:r,checked:i,defaultChecked:o,required:s,disabled:a,value:l,onCheckedChange:u,form:d,...c}=e;return h.jsx(jb,{__scopeCheckbox:n,checked:i,defaultChecked:o,disabled:a,required:s,onCheckedChange:u,name:r,form:d,value:l,internal_do_not_use_render:({isFormControl:f})=>h.jsxs(h.Fragment,{children:[h.jsx(gv,{...c,ref:t,__scopeCheckbox:n}),f&&h.jsx(wv,{__scopeCheckbox:n})]})})});Cc.displayName=zs;var vv="CheckboxIndicator",yv=y.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:r,...i}=e,o=Sc(vv,n);return h.jsx(wc,{present:r||sn(o.checked)||o.checked===!0,children:h.jsx(ye.span,{"data-state":Sv(o.checked),"data-disabled":o.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});yv.displayName=vv;var xv="CheckboxBubbleInput",wv=y.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:r,hasConsumerStoppedPropagationRef:i,checked:o,defaultChecked:s,required:a,disabled:l,name:u,value:d,form:c,bubbleInput:f,setBubbleInput:g}=Sc(xv,e),w=je(n,g),x=fv(o),C=pv(r);y.useEffect(()=>{const p=f;if(!p)return;const m=window.HTMLInputElement.prototype,k=Object.getOwnPropertyDescriptor(m,"checked").set,b=!i.current;if(x!==o&&k){const P=new Event("click",{bubbles:b});p.indeterminate=sn(o),k.call(p,sn(o)?!1:o),p.dispatchEvent(P)}},[f,x,o,i]);const v=y.useRef(sn(o)?!1:o);return h.jsx(ye.input,{type:"checkbox","aria-hidden":!0,defaultChecked:s??v.current,required:a,disabled:l,name:u,value:d,form:c,...t,tabIndex:-1,ref:w,style:{...t.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});wv.displayName=xv;function Rb(e){return typeof e=="function"}function sn(e){return e==="indeterminate"}function Sv(e){return sn(e)?"indeterminate":e?"checked":"unchecked"}const Cv=re.forwardRef(({className:e,...t},n)=>h.jsx(Cc,{ref:n,className:Ye("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:h.jsx(yv,{className:Ye("flex items-center justify-center text-current"),children:h.jsx(mP,{className:"h-4 w-4"})})}));Cv.displayName=Cc.displayName;var Mb="Label",kv=y.forwardRef((e,t)=>h.jsx(ye.label,{...e,ref:t,onMouseDown:n=>{var i;n.target.closest("button, input, select, textarea")||((i=e.onMouseDown)==null||i.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));kv.displayName=Mb;var Pv=kv;const Ab=vc("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),ut=re.forwardRef(({className:e,...t},n)=>h.jsx(Pv,{ref:n,className:Ye(Ab(),e),...t}));ut.displayName=Pv.displayName;function bv(e,[t,n]){return Math.min(n,Math.max(t,e))}var _b=y.createContext(void 0);function Lb(e){const t=y.useContext(_b);return e||t||"ltr"}function Ev(e){const t=e+"CollectionProvider",[n,r]=Fs(t),[i,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=x=>{const{scope:C,children:v}=x,p=re.useRef(null),m=re.useRef(new Map).current;return h.jsx(i,{scope:C,itemMap:m,collectionRef:p,children:v})};s.displayName=t;const a=e+"CollectionSlot",l=cs(a),u=re.forwardRef((x,C)=>{const{scope:v,children:p}=x,m=o(a,v),S=je(C,m.collectionRef);return h.jsx(l,{ref:S,children:p})});u.displayName=a;const d=e+"CollectionItemSlot",c="data-radix-collection-item",f=cs(d),g=re.forwardRef((x,C)=>{const{scope:v,children:p,...m}=x,S=re.useRef(null),k=je(C,S),b=o(d,v);return re.useEffect(()=>(b.itemMap.set(S,{ref:S,...m}),()=>void b.itemMap.delete(S))),h.jsx(f,{[c]:"",ref:k,children:p})});g.displayName=d;function w(x){const C=o(e+"CollectionConsumer",x);return re.useCallback(()=>{const p=C.collectionRef.current;if(!p)return[];const m=Array.from(p.querySelectorAll(`[${c}]`));return Array.from(C.itemMap.values()).sort((b,P)=>m.indexOf(b.ref.current)-m.indexOf(P.ref.current))},[C.collectionRef,C.itemMap])}return[{Provider:s,Slot:u,ItemSlot:g},w,r]}var Tv=["PageUp","PageDown"],Nv=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],jv={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},Rr="Slider",[Hl,Db,Vb]=Ev(Rr),[Rv,QE]=Fs(Rr,[Vb]),[Ib,Bs]=Rv(Rr),Mv=y.forwardRef((e,t)=>{const{name:n,min:r=0,max:i=100,step:o=1,orientation:s="horizontal",disabled:a=!1,minStepsBetweenThumbs:l=0,defaultValue:u=[r],value:d,onValueChange:c=()=>{},onValueCommit:f=()=>{},inverted:g=!1,form:w,...x}=e,C=y.useRef(new Set),v=y.useRef(0),m=s==="horizontal"?Ob:Fb,[S=[],k]=xc({prop:d,defaultProp:u,onChange:O=>{var H;(H=[...C.current][v.current])==null||H.focus(),c(O)}}),b=y.useRef(S);function P(O){const D=Wb(S,O);j(O,D)}function E(O){j(O,v.current)}function R(){const O=b.current[v.current];S[v.current]!==O&&f(S)}function j(O,D,{commit:H}={commit:!1}){const A=Qb(o),G=Yb(Math.round((O-r)/o)*o+r,A),z=bv(G,[r,i]);k((Q=[])=>{const N=Ub(Q,z,D);if(Gb(N,l*o)){v.current=N.indexOf(z);const _=String(N)!==String(Q);return _&&H&&f(N),_?N:Q}else return Q})}return h.jsx(Ib,{scope:e.__scopeSlider,name:n,disabled:a,min:r,max:i,valueIndexToChangeRef:v,thumbs:C.current,values:S,orientation:s,form:w,children:h.jsx(Hl.Provider,{scope:e.__scopeSlider,children:h.jsx(Hl.Slot,{scope:e.__scopeSlider,children:h.jsx(m,{"aria-disabled":a,"data-disabled":a?"":void 0,...x,ref:t,onPointerDown:fe(x.onPointerDown,()=>{a||(b.current=S)}),min:r,max:i,inverted:g,onSlideStart:a?void 0:P,onSlideMove:a?void 0:E,onSlideEnd:a?void 0:R,onHomeKeyDown:()=>!a&&j(r,0,{commit:!0}),onEndKeyDown:()=>!a&&j(i,S.length-1,{commit:!0}),onStepKeyDown:({event:O,direction:D})=>{if(!a){const G=Tv.includes(O.key)||O.shiftKey&&Nv.includes(O.key)?10:1,z=v.current,Q=S[z],N=o*G*D;j(Q+N,z,{commit:!0})}}})})})})});Mv.displayName=Rr;var[Av,_v]=Rv(Rr,{startEdge:"left",endEdge:"right",size:"width",direction:1}),Ob=y.forwardRef((e,t)=>{const{min:n,max:r,dir:i,inverted:o,onSlideStart:s,onSlideMove:a,onSlideEnd:l,onStepKeyDown:u,...d}=e,[c,f]=y.useState(null),g=je(t,m=>f(m)),w=y.useRef(void 0),x=Lb(i),C=x==="ltr",v=C&&!o||!C&&o;function p(m){const S=w.current||c.getBoundingClientRect(),k=[0,S.width],P=kc(k,v?[n,r]:[r,n]);return w.current=S,P(m-S.left)}return h.jsx(Av,{scope:e.__scopeSlider,startEdge:v?"left":"right",endEdge:v?"right":"left",direction:v?1:-1,size:"width",children:h.jsx(Lv,{dir:x,"data-orientation":"horizontal",...d,ref:g,style:{...d.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:m=>{const S=p(m.clientX);s==null||s(S)},onSlideMove:m=>{const S=p(m.clientX);a==null||a(S)},onSlideEnd:()=>{w.current=void 0,l==null||l()},onStepKeyDown:m=>{const k=jv[v?"from-left":"from-right"].includes(m.key);u==null||u({event:m,direction:k?-1:1})}})})}),Fb=y.forwardRef((e,t)=>{const{min:n,max:r,inverted:i,onSlideStart:o,onSlideMove:s,onSlideEnd:a,onStepKeyDown:l,...u}=e,d=y.useRef(null),c=je(t,d),f=y.useRef(void 0),g=!i;function w(x){const C=f.current||d.current.getBoundingClientRect(),v=[0,C.height],m=kc(v,g?[r,n]:[n,r]);return f.current=C,m(x-C.top)}return h.jsx(Av,{scope:e.__scopeSlider,startEdge:g?"bottom":"top",endEdge:g?"top":"bottom",size:"height",direction:g?1:-1,children:h.jsx(Lv,{"data-orientation":"vertical",...u,ref:c,style:{...u.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:x=>{const C=w(x.clientY);o==null||o(C)},onSlideMove:x=>{const C=w(x.clientY);s==null||s(C)},onSlideEnd:()=>{f.current=void 0,a==null||a()},onStepKeyDown:x=>{const v=jv[g?"from-bottom":"from-top"].includes(x.key);l==null||l({event:x,direction:v?-1:1})}})})}),Lv=y.forwardRef((e,t)=>{const{__scopeSlider:n,onSlideStart:r,onSlideMove:i,onSlideEnd:o,onHomeKeyDown:s,onEndKeyDown:a,onStepKeyDown:l,...u}=e,d=Bs(Rr,n);return h.jsx(ye.span,{...u,ref:t,onKeyDown:fe(e.onKeyDown,c=>{c.key==="Home"?(s(c),c.preventDefault()):c.key==="End"?(a(c),c.preventDefault()):Tv.concat(Nv).includes(c.key)&&(l(c),c.preventDefault())}),onPointerDown:fe(e.onPointerDown,c=>{const f=c.target;f.setPointerCapture(c.pointerId),c.preventDefault(),d.thumbs.has(f)?f.focus():r(c)}),onPointerMove:fe(e.onPointerMove,c=>{c.target.hasPointerCapture(c.pointerId)&&i(c)}),onPointerUp:fe(e.onPointerUp,c=>{const f=c.target;f.hasPointerCapture(c.pointerId)&&(f.releasePointerCapture(c.pointerId),o(c))})})}),Dv="SliderTrack",Vv=y.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,i=Bs(Dv,n);return h.jsx(ye.span,{"data-disabled":i.disabled?"":void 0,"data-orientation":i.orientation,...r,ref:t})});Vv.displayName=Dv;var Kl="SliderRange",Iv=y.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,i=Bs(Kl,n),o=_v(Kl,n),s=y.useRef(null),a=je(t,s),l=i.values.length,u=i.values.map(f=>zv(f,i.min,i.max)),d=l>1?Math.min(...u):0,c=100-Math.max(...u);return h.jsx(ye.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,...r,ref:a,style:{...e.style,[o.startEdge]:d+"%",[o.endEdge]:c+"%"}})});Iv.displayName=Kl;var Gl="SliderThumb",Ov=y.forwardRef((e,t)=>{const n=Db(e.__scopeSlider),[r,i]=y.useState(null),o=je(t,a=>i(a)),s=y.useMemo(()=>r?n().findIndex(a=>a.ref.current===r):-1,[n,r]);return h.jsx(zb,{...e,ref:o,index:s})}),zb=y.forwardRef((e,t)=>{const{__scopeSlider:n,index:r,name:i,...o}=e,s=Bs(Gl,n),a=_v(Gl,n),[l,u]=y.useState(null),d=je(t,p=>u(p)),c=l?s.form||!!l.closest("form"):!0,f=pv(l),g=s.values[r],w=g===void 0?0:zv(g,s.min,s.max),x=$b(r,s.values.length),C=f==null?void 0:f[a.size],v=C?Hb(C,w,a.direction):0;return y.useEffect(()=>{if(l)return s.thumbs.add(l),()=>{s.thumbs.delete(l)}},[l,s.thumbs]),h.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[a.startEdge]:`calc(${w}% + ${v}px)`},children:[h.jsx(Hl.ItemSlot,{scope:e.__scopeSlider,children:h.jsx(ye.span,{role:"slider","aria-label":e["aria-label"]||x,"aria-valuemin":s.min,"aria-valuenow":g,"aria-valuemax":s.max,"aria-orientation":s.orientation,"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,tabIndex:s.disabled?void 0:0,...o,ref:d,style:g===void 0?{display:"none"}:e.style,onFocus:fe(e.onFocus,()=>{s.valueIndexToChangeRef.current=r})})}),c&&h.jsx(Fv,{name:i??(s.name?s.name+(s.values.length>1?"[]":""):void 0),form:s.form,value:g},r)]})});Ov.displayName=Gl;var Bb="RadioBubbleInput",Fv=y.forwardRef(({__scopeSlider:e,value:t,...n},r)=>{const i=y.useRef(null),o=je(i,r),s=fv(t);return y.useEffect(()=>{const a=i.current;if(!a)return;const l=window.HTMLInputElement.prototype,d=Object.getOwnPropertyDescriptor(l,"value").set;if(s!==t&&d){const c=new Event("input",{bubbles:!0});d.call(a,t),a.dispatchEvent(c)}},[s,t]),h.jsx(ye.input,{style:{display:"none"},...n,ref:o,defaultValue:t})});Fv.displayName=Bb;function Ub(e=[],t,n){const r=[...e];return r[n]=t,r.sort((i,o)=>i-o)}function zv(e,t,n){const o=100/(n-t)*(e-t);return bv(o,[0,100])}function $b(e,t){return t>2?`Value ${e+1} of ${t}`:t===2?["Minimum","Maximum"][e]:void 0}function Wb(e,t){if(e.length===1)return 0;const n=e.map(i=>Math.abs(i-t)),r=Math.min(...n);return n.indexOf(r)}function Hb(e,t,n){const r=e/2,o=kc([0,50],[0,r]);return(r-o(t)*n)*n}function Kb(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}function Gb(e,t){if(t>0){const n=Kb(e);return Math.min(...n)>=t}return!0}function kc(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function Qb(e){return(String(e).split(".")[1]||"").length}function Yb(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}var Bv=Mv,Xb=Vv,qb=Iv,rp=Ov;const Uv=re.forwardRef(({className:e,...t},n)=>h.jsxs(Bv,{ref:n,className:Ye("relative flex w-full touch-none select-none items-center",e),...t,children:[h.jsx(Xb,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:h.jsx(qb,{className:"absolute h-full bg-primary"})}),h.jsx(rp,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"}),t.defaultValue&&t.defaultValue.length>1&&h.jsx(rp,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));Uv.displayName=Bv.displayName;const Zb=[{id:1,name:"Rose Crochet Bag",category:"Crochet",price:"28.00",imageSrc:"crochet_bag_rose_main",description:"Elegant rose-colored crochet bag.",tags:["bag","fashion"],images:["crochet_bag_rose_main","crochet_bag_rose_detail"],options:{colors:[{name:"Rose",value:"#F2BED1"}],sizes:[{name:"Medium"}]}},{id:2,name:"Lavender Scented Candle",category:"Candles",price:"16.00",imageSrc:"candle_lavender_main",description:"Calming lavender scented candle.",tags:["home","relax"],images:["candle_lavender_main","candle_lavender_burning"],options:{scents:[{name:"Lavender"}],sizes:[{name:"4oz"}]}},{id:3,name:"Minimalist Concrete Planter",category:"Concrete",price:"22.00",imageSrc:"concrete_planter_main",description:"Sleek minimalist concrete planter.",tags:["decor","plants"],images:["concrete_planter_main","concrete_planter_with_plant"],options:{colors:[{name:"Natural Grey",value:"#D1D5DB"}]}},{id:4,name:"Abstract Mini Canvas",category:"Canvas",price:"35.00",imageSrc:"canvas_abstract_main",description:"Vibrant abstract mini canvas art.",tags:["art","decor"],images:["canvas_abstract_main","canvas_abstract_detail"],options:{styles:[{name:"Pastel Dreams"}]}},{id:5,name:"Pastel Scrunchie Set",category:"Crochet",price:"12.00",imageSrc:"crochet_scrunchies_pastel",description:"Set of 3 pastel crochet scrunchies.",tags:["hair","accessory"]},{id:6,name:"Ocean Breeze Candle",category:"Candles",price:"18.00",imageSrc:"candle_ocean_breeze",description:"Refreshing ocean breeze scented candle.",tags:["home","fresh"]},{id:7,name:"Geometric Clay Earrings",category:"Clay",price:"20.00",imageSrc:"clay_earrings_geometric",description:"Handmade geometric clay earrings.",tags:["jewelry","fashion"]},{id:8,name:"Concrete Coaster Set",category:"Concrete",price:"25.00",imageSrc:"concrete_coasters_set",description:"Set of 4 modern concrete coasters.",tags:["home","decor"]},{id:9,name:"Floral Wall Piece",category:"Canvas",price:"45.00",imageSrc:"canvas_floral_wall",description:"Beautiful floral canvas wall art.",tags:["art","wall decor"]},{id:10,name:"Cute Crochet Clip",category:"Crochet",price:"8.00",imageSrc:"crochet_clip_cute",description:"Adorable crochet hair clip.",tags:["hair","accessory"]},{id:11,name:"Vanilla Bean Candle",category:"Candles",price:"17.00",imageSrc:"candle_vanilla_bean",description:"Warm vanilla bean scented candle.",tags:["home","cozy"]},{id:12,name:"Marble Clay Dish",category:"Clay",price:"15.00",imageSrc:"clay_dish_marble",description:"Elegant marble effect clay trinket dish.",tags:["decor","storage"]}],ip=["All","Crochet","Candles","Crafts","Clay","Concrete","Canvas"],Jb=()=>{const e=zn(),[t,n]=y.useState(["All"]),[r,i]=y.useState([0,50]),[o,s]=y.useState(!1);y.useEffect(()=>{const f=new URLSearchParams(e.search).get("category");if(f&&ip.map(g=>g.toLowerCase()).includes(f)){const g=f.charAt(0).toUpperCase()+f.slice(1);n([g])}else n(["All"])},[e.search]);const a=c=>{n(f=>{if(c==="All")return["All"];const g=f.includes("All")?[]:[...f];if(g.includes(c)){const w=g.filter(x=>x!==c);return w.length===0?["All"]:w}else return[...g,c]})},l=y.useMemo(()=>Zb.filter(c=>{const f=t.includes("All")||t.includes(c.category),g=parseFloat(c.price)>=r[0]&&parseFloat(c.price)<=r[1];return f&&g}),[t,r]),u={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},d={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{type:"spring",stiffness:100}}};return h.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 animate-fade-in",children:[h.jsx(L.h1,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},transition:{duration:.6,ease:"easeOut"},className:"text-4xl md:text-5xl font-bold text-pastel-accent text-center mb-12",children:"Our Handmade Collection"}),h.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[h.jsxs(L.aside,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.5,delay:.2},className:`md:w-1/4 bg-pastel-light p-6 rounded-xl shadow-lg md:sticky md:top-24 h-fit ${o?"block":"hidden"} md:block`,children:[h.jsxs("div",{className:"flex justify-between items-center mb-6",children:[h.jsx("h2",{className:"text-2xl font-semibold text-pastel-accent",children:"Filters"}),h.jsx(ce,{variant:"ghost",size:"icon",className:"md:hidden text-pastel-accent",onClick:()=>s(!1),children:h.jsx(mc,{size:24})})]}),h.jsxs("div",{className:"mb-8",children:[h.jsx("h3",{className:"text-lg font-medium text-pastel-accent mb-3",children:"Categories"}),h.jsx("div",{className:"space-y-2",children:ip.map(c=>h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx(Cv,{id:c,checked:t.includes(c),onCheckedChange:()=>a(c),className:"data-[state=checked]:bg-pastel-dark data-[state=checked]:text-pastel-bg border-pastel-medium"}),h.jsx(ut,{htmlFor:c,className:"text-pastel-accent/90 cursor-pointer hover:text-pastel-dark",children:c})]},c))})]}),h.jsxs("div",{children:[h.jsx("h3",{className:"text-lg font-medium text-pastel-accent mb-3",children:"Price Range"}),h.jsx(Uv,{defaultValue:[0,50],min:0,max:50,step:1,value:r,onValueChange:i,className:"[&>span:first-child]:h-1 [&>span:first-child]:bg-pastel-medium [&_[role=slider]]:bg-pastel-dark [&_[role=slider]]:border-pastel-dark [&_[role=slider]]:shadow-md"}),h.jsxs("div",{className:"flex justify-between text-sm text-pastel-accent/80 mt-2",children:[h.jsxs("span",{children:["$",r[0]]}),h.jsxs("span",{children:["$",r[1]]})]})]})]}),h.jsxs("main",{className:"md:w-3/4",children:[h.jsx("div",{className:"md:hidden mb-6 flex justify-end",children:h.jsxs(ce,{variant:"outline",className:"border-pastel-dark text-pastel-accent",onClick:()=>s(!0),children:[h.jsx(xP,{size:18,className:"mr-2"})," Filters"]})}),l.length>0?h.jsx(L.div,{variants:u,initial:"hidden",animate:"visible",className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:l.map((c,f)=>h.jsx(L.div,{variants:d,custom:f,className:"animate-slide-in-up",style:{animationDelay:`${f*.05}s`},children:h.jsx(yc,{product:c})},c.id))}):h.jsx(L.p,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:"text-center text-xl text-pastel-accent/70 py-10",children:"No products match your current filters. Try adjusting them!"})]})]})]})},eE=()=>{const e={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}},exit:{opacity:0,y:-20,transition:{duration:.5,ease:"easeIn"}}},t={hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:.7,ease:"easeOut"}}};return h.jsxs(L.div,{initial:"initial",animate:"animate",exit:"exit",variants:e,className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 animate-fade-in",children:[h.jsx(L.h1,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1,ease:"easeOut"},className:"text-4xl md:text-5xl font-bold text-pastel-accent text-center mb-12",children:"Our Story: Handmade with Heart"}),h.jsxs(L.section,{variants:t,initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},className:"mb-16 grid md:grid-cols-2 gap-12 items-center",children:[h.jsxs("div",{className:"prose prose-lg max-w-none text-pastel-accent/90",children:[h.jsx("p",{className:"lead text-xl",children:'Welcome to C³ – C Cube, where creativity and craftsmanship intertwine. Our name, "C Cube," represents our passion for diverse crafts, spanning six core categories: Crochet, Candles, Crafts, Clay, Concrete, and Canvas. Each piece is a labor of love, meticulously handmade with a touch of pastel charm and a cozy, feminine aesthetic.'}),h.jsx("p",{children:"Our journey began with a simple desire: to share the joy of handmade goods that bring warmth and personality to everyday life. We believe in the beauty of imperfection and the unique story each handcrafted item tells. From delicate crochet scrunchies to minimalist concrete decor, every product in our shop is designed to evoke a sense of comfort and delight."}),h.jsx("p",{children:"At C³ – C Cube, we pour our hearts into every creation, ensuring quality and artistry. We hope our items find a special place in your home and heart, adding a sprinkle of handmade magic to your world."})]}),h.jsx(L.div,{className:"rounded-xl overflow-hidden shadow-2xl",initial:{scale:.8,opacity:0},whileInView:{scale:1,opacity:1},transition:{duration:.6,delay:.2,ease:"easeOut"},viewport:{once:!0,amount:.3},children:h.jsx("img",{class:"w-full h-auto object-cover",alt:"Founder working on crafts",src:"https://images.unsplash.com/photo-1607063696672-9dbc90ef3ebf"})})]}),h.jsxs(L.section,{variants:t,initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},className:"py-16 bg-pastel-light rounded-xl shadow-lg",children:[h.jsx("h2",{className:"text-3xl font-bold text-pastel-accent text-center mb-12",children:"Our Values"}),h.jsxs("div",{className:"grid md:grid-cols-3 gap-8 text-center px-8",children:[h.jsxs(L.div,{className:"p-6",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0},children:[h.jsx(ji,{size:48,className:"mx-auto mb-4 text-pastel-dark"}),h.jsx("h3",{className:"text-2xl font-semibold text-pastel-accent mb-2",children:"Passion-Driven"}),h.jsx("p",{className:"text-pastel-accent/80",children:"Every item is created with genuine love and enthusiasm for the craft. We believe this passion shines through in the quality and uniqueness of our products."})]}),h.jsxs(L.div,{className:"p-6",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},children:[h.jsx(PP,{size:48,className:"mx-auto mb-4 text-pastel-dark"}),h.jsx("h3",{className:"text-2xl font-semibold text-pastel-accent mb-2",children:"Quality Craftsmanship"}),h.jsx("p",{className:"text-pastel-accent/80",children:"We use high-quality materials and pay meticulous attention to detail, ensuring each piece is beautiful, durable, and well-made."})]}),h.jsxs(L.div,{className:"p-6",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.3},viewport:{once:!0},children:[h.jsx(NP,{size:48,className:"mx-auto mb-4 text-pastel-dark"}),h.jsx("h3",{className:"text-2xl font-semibold text-pastel-accent mb-2",children:"Customer Joy"}),h.jsx("p",{className:"text-pastel-accent/80",children:"Our greatest reward is bringing a smile to your face. We strive to create pieces that you'll cherish and that add a touch of happiness to your life."})]})]})]}),h.jsxs(L.section,{variants:t,initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},className:"mt-16 text-center",children:[h.jsx("h2",{className:"text-3xl font-bold text-pastel-accent mb-6",children:"Meet the Maker"}),h.jsxs("div",{className:"max-w-3xl mx-auto",children:[h.jsx(L.div,{className:"w-40 h-40 mx-auto mb-6 rounded-full overflow-hidden shadow-xl border-4 border-pastel-medium",initial:{scale:.5,opacity:0},whileInView:{scale:1,opacity:1},transition:{duration:.6,type:"spring",stiffness:120},viewport:{once:!0},children:h.jsx("img",{class:"w-full h-full object-cover",alt:"Portrait of the shop owner",src:"https://images.unsplash.com/photo-1633887091219-265c29eac4cd"})}),h.jsx("p",{className:"text-xl text-pastel-accent/90 mb-4",children:"Hi, I'm [Your Name/Fictional Name], the hands and heart behind C³ – C Cube! Crafting has always been my sanctuary, a way to express creativity and find peace. I started this little shop to share my passion with you and to spread a little bit of handmade joy. Thank you for supporting my dream!"})]})]})]})},Ft=re.forwardRef(({className:e,type:t,...n},r)=>h.jsx("input",{type:t,className:Ye("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));Ft.displayName="Input";const Ql=re.forwardRef(({className:e,...t},n)=>h.jsx("textarea",{className:Ye("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));Ql.displayName="Textarea";const tE=()=>{const{toast:e}=zi(),[t,n]=y.useState({name:"",email:"",subject:"",message:""}),[r,i]=y.useState({name:"",email:"",itemType:"",description:"",quantity:1}),o=(d,c)=>{const{name:f,value:g}=d.target;c==="contact"?n(w=>({...w,[f]:g})):i(w=>({...w,[f]:g}))},s=(d,c)=>{d.preventDefault(),c==="contact"?(console.log("Contact Form Data:",t),e({title:"💌 Message Sent!",description:"Thanks for reaching out! We'll get back to you soon.",duration:5e3}),n({name:"",email:"",subject:"",message:""})):(console.log("Custom Order Data:",r),e({title:"✨ Custom Order Request Submitted!",description:"We've received your request and will be in touch to discuss the details!",duration:5e3}),i({name:"",email:"",itemType:"",description:"",quantity:1}))},a={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut",staggerChildren:.2}}},l={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5,ease:"easeOut"}}},u=[{icon:h.jsx(nv,{size:24,className:"text-pastel-dark"}),text:"<EMAIL>",href:"mailto:<EMAIL>"},{icon:h.jsx(bP,{size:24,className:"text-pastel-dark"}),text:"(*************",href:"tel:1234567890"},{icon:h.jsx(CP,{size:24,className:"text-pastel-dark"}),text:"123 Pastel Lane, Craftsville, CA 90210",href:"#"}];return h.jsxs(L.div,{variants:a,initial:"initial",animate:"animate",className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 animate-fade-in",children:[h.jsx(L.h1,{variants:l,className:"text-4xl md:text-5xl font-bold text-pastel-accent text-center mb-12",children:"Get In Touch"}),h.jsxs("div",{className:"grid md:grid-cols-2 gap-12 md:gap-16 mb-16",children:[h.jsxs(L.div,{variants:l,className:"space-y-8",children:[h.jsx("h2",{className:"text-2xl md:text-3xl font-semibold text-pastel-accent",children:"We'd love to hear from you!"}),h.jsx("p",{className:"text-pastel-accent/80 text-lg",children:"Whether you have a question about our products, an idea for a custom piece, or just want to say hello, feel free to reach out. We're always happy to connect with fellow craft lovers!"}),h.jsx("div",{className:"space-y-6",children:u.map((d,c)=>h.jsxs(L.a,{href:d.href,className:"flex items-center space-x-4 group",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.4,delay:.2+c*.1},children:[h.jsx("span",{className:"flex-shrink-0 p-3 bg-pastel-light rounded-full group-hover:bg-pastel-medium transition-colors duration-300",children:d.icon}),h.jsx("span",{className:"text-pastel-accent group-hover:text-pastel-dark transition-colors duration-300",children:d.text})]},c))})]}),h.jsxs(L.div,{variants:l,className:"bg-pastel-light p-8 rounded-xl shadow-xl",children:[h.jsx("h2",{className:"text-2xl font-semibold text-pastel-accent mb-6",children:"Send Us a Message"}),h.jsxs("form",{onSubmit:d=>s(d,"contact"),className:"space-y-6",children:[h.jsxs("div",{children:[h.jsx(ut,{htmlFor:"name",className:"text-pastel-accent/90",children:"Full Name"}),h.jsx(Ft,{type:"text",name:"name",id:"name",value:t.name,onChange:d=>o(d,"contact"),required:!0,className:"bg-white border-pastel-medium focus:border-pastel-dark focus:ring-pastel-dark"})]}),h.jsxs("div",{children:[h.jsx(ut,{htmlFor:"email",className:"text-pastel-accent/90",children:"Email Address"}),h.jsx(Ft,{type:"email",name:"email",id:"email",value:t.email,onChange:d=>o(d,"contact"),required:!0,className:"bg-white border-pastel-medium focus:border-pastel-dark focus:ring-pastel-dark"})]}),h.jsxs("div",{children:[h.jsx(ut,{htmlFor:"subject",className:"text-pastel-accent/90",children:"Subject"}),h.jsx(Ft,{type:"text",name:"subject",id:"subject",value:t.subject,onChange:d=>o(d,"contact"),required:!0,className:"bg-white border-pastel-medium focus:border-pastel-dark focus:ring-pastel-dark"})]}),h.jsxs("div",{children:[h.jsx(ut,{htmlFor:"message",className:"text-pastel-accent/90",children:"Your Message"}),h.jsx(Ql,{name:"message",id:"message",rows:5,value:t.message,onChange:d=>o(d,"contact"),required:!0,className:"bg-white border-pastel-medium focus:border-pastel-dark focus:ring-pastel-dark"})]}),h.jsxs(ce,{type:"submit",className:"w-full bg-pastel-accent text-pastel-bg hover:bg-pastel-accent/90 shadow-md",children:["Send Message ",h.jsx(EP,{size:18,className:"ml-2"})]})]})]})]}),h.jsxs(L.section,{id:"custom-orders",variants:l,className:"py-12 md:py-16 bg-gradient-to-br from-pastel-medium to-pastel-dark rounded-xl shadow-2xl p-8 md:p-12",children:[h.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white text-center mb-4",children:"Dreaming of Something Special?"}),h.jsx("p",{className:"text-lg text-white/90 text-center mb-10 max-w-2xl mx-auto",children:"Let us craft a unique piece just for you! Fill out the form below with your ideas, and we'll get back to you to discuss the possibilities."}),h.jsxs("form",{onSubmit:d=>s(d,"customOrder"),className:"space-y-6 max-w-xl mx-auto bg-pastel-light/80 p-8 rounded-lg shadow-inner",children:[h.jsxs("div",{children:[h.jsx(ut,{htmlFor:"customName",className:"text-pastel-accent/90",children:"Full Name"}),h.jsx(Ft,{type:"text",name:"name",id:"customName",value:r.name,onChange:d=>o(d,"customOrder"),required:!0,className:"bg-white border-pastel-medium focus:border-pastel-dark focus:ring-pastel-dark"})]}),h.jsxs("div",{children:[h.jsx(ut,{htmlFor:"customEmail",className:"text-pastel-accent/90",children:"Email Address"}),h.jsx(Ft,{type:"email",name:"email",id:"customEmail",value:r.email,onChange:d=>o(d,"customOrder"),required:!0,className:"bg-white border-pastel-medium focus:border-pastel-dark focus:ring-pastel-dark"})]}),h.jsxs("div",{children:[h.jsx(ut,{htmlFor:"itemType",className:"text-pastel-accent/90",children:"Type of Item (e.g., Crochet Bag, Scented Candle)"}),h.jsx(Ft,{type:"text",name:"itemType",id:"itemType",value:r.itemType,onChange:d=>o(d,"customOrder"),required:!0,placeholder:"e.g., Crochet Amigurumi, Custom Scented Candle",className:"bg-white border-pastel-medium focus:border-pastel-dark focus:ring-pastel-dark"})]}),h.jsxs("div",{children:[h.jsx(ut,{htmlFor:"description",className:"text-pastel-accent/90",children:"Describe Your Custom Request"}),h.jsx(Ql,{name:"description",id:"description",rows:5,value:r.description,onChange:d=>o(d,"customOrder"),required:!0,placeholder:"Include details like colors, size, specific design ideas, etc.",className:"bg-white border-pastel-medium focus:border-pastel-dark focus:ring-pastel-dark"})]}),h.jsxs("div",{children:[h.jsx(ut,{htmlFor:"quantity",className:"text-pastel-accent/90",children:"Quantity"}),h.jsx(Ft,{type:"number",name:"quantity",id:"quantity",min:"1",value:r.quantity,onChange:d=>o(d,"customOrder"),required:!0,className:"bg-white border-pastel-medium focus:border-pastel-dark focus:ring-pastel-dark"})]}),h.jsx(ce,{type:"submit",className:"w-full bg-pastel-accent text-pastel-bg hover:bg-pastel-accent/90 shadow-md",children:"Submit Custom Order Request"})]})]})]})},[nE,rE]=y.useState([]);y.useEffect(()=>{(async()=>{try{const n=(await getDocs(collection(db,"products"))).docs.map(r=>({id:r.id,...r.data()}));rE(n)}catch(t){console.error("Error fetching products:",t)}})()},[]);const iE=[{id:1,name:"Rose Crochet Bag",category:"Crochet",price:"28.00",description:"Elegant and spacious rose-colored crochet bag, perfect for daily essentials or a chic outing. Handcrafted with soft, durable yarn.",longDescription:"This beautifully handcrafted crochet bag features an intricate stitch pattern in a lovely rose pastel hue. It's lined with a complementary fabric and includes an inner pocket for small items. The sturdy straps ensure comfortable carrying. A versatile accessory that adds a touch of handmade charm to any outfit.",tags:["bag","fashion","handmade","pastel"],images:["crochet_bag_rose_main","crochet_bag_rose_detail","crochet_bag_rose_lifestyle"],options:{colors:[{name:"Rose",value:"#F2BED1",default:!0},{name:"Mint",value:"#A0E7E5"},{name:"Lavender",value:"#B4A0E5"}],sizes:[{name:"Small",dimensions:"20x15cm",default:!0},{name:"Medium",dimensions:"25x20cm"},{name:"Large",dimensions:"30x25cm"}]},stock:15,rating:4.8,reviews:23,sku:"CRB001-ROS-S"},{id:2,name:"Lavender Scented Candle",category:"Candles",price:"16.00",description:"Calming lavender scented soy candle, hand-poured for a soothing ambiance. Ideal for relaxation and unwinding.",longDescription:"Indulge in the tranquil aroma of our Lavender Scented Candle. Made with 100% natural soy wax and premium lavender essential oils, this candle burns cleanly for up to 40 hours. Housed in a charming pastel-colored jar, it's a beautiful addition to any room, promoting peace and serenity.",tags:["home","relax","aromatherapy","soy wax"],images:["candle_lavender_main","candle_lavender_burning","candle_lavender_packaging"],options:{scents:[{name:"Lavender",default:!0},{name:"Vanilla Bean"},{name:"Ocean Breeze"}],sizes:[{name:"4oz",burnTime:"20-25 hrs",default:!0},{name:"8oz",burnTime:"40-50 hrs"}]},stock:30,rating:4.9,reviews:45,sku:"CND001-LAV-4OZ"},{id:3,name:"Minimalist Concrete Planter",category:"Concrete",price:"22.00",description:"Sleek minimalist concrete planter for small succulents or cacti. Adds a modern touch to your home or office decor.",longDescription:"Crafted from high-quality concrete, this minimalist planter boasts a smooth finish and a contemporary geometric design. It's perfect for housing your favorite small plants, bringing a touch of nature indoors with an urban chic aesthetic. Includes a drainage hole and optional saucer.",tags:["decor","plants","modern","minimalist"],images:["concrete_planter_main","concrete_planter_with_plant","concrete_planter_group"],options:{colors:[{name:"Natural Grey",value:"#D1D5DB",default:!0},{name:"Pastel Pink",value:"#FDCEDF"},{name:"White Terrazzo",value:"#FFFFFF"}],finish:[{name:"Matte",default:!0},{name:"Sealed Gloss"}]},stock:20,rating:4.7,reviews:18,sku:"CON001-GRY-MAT"},{id:4,name:"Abstract Mini Canvas",category:"Canvas",price:"35.00",description:"Vibrant abstract mini canvas art, perfect for adding a pop of color to small spaces. Original hand-painted piece.",longDescription:"This unique mini canvas features a stunning abstract design, hand-painted with acrylics in a harmonious pastel palette. Measuring 6x6 inches, it's an ideal accent for a desk, shelf, or gallery wall. Each piece is original and signed by the artist, bringing a touch of exclusive art to your collection.",tags:["art","decor","original","painting"],images:["canvas_abstract_main","canvas_abstract_detail","canvas_abstract_on_shelf"],options:{styles:[{name:"Pastel Dreams",default:!0},{name:"Ocean Hues"},{name:"Sunset Glow"}],framing:[{name:"Unframed",default:!0},{name:"Floating Frame (White)"}]},stock:5,rating:5,reviews:12,sku:"CNV001-PAS-UNF"}],oE=iE.slice(0,3),sE=()=>{const{productId:e}=w1(),{toast:t}=zi(),[n,r]=y.useState(null),[i,o]=y.useState({}),[s,a]=y.useState(1),[l,u]=y.useState(0);y.useEffect(()=>{const p=nE.find(m=>m.id.toString()===e);if(r(p),p&&p.options){const m={};Object.keys(p.options).forEach(S=>{const k=p.options[S].find(b=>b.default);k?m[S]=k.name:p.options[S].length>0&&(m[S]=p.options[S][0].name)}),o(m)}a(1),u(0)},[e]);const d=(p,m)=>{o(S=>({...S,[p]:m}))},c=p=>{a(m=>Math.max(1,Math.min(m+p,(n==null?void 0:n.stock)||1)))},f=()=>{t({title:"💖 Added to Cart!",description:`${n.name} (${Object.values(i).join(", ")}) x${s} is now in your cart.`,duration:3e3,action:h.jsx(Xf,{className:"text-green-500"})})},g=()=>{t({title:"❤️ Added to Wishlist!",description:`${n.name} has been added to your wishlist.`,duration:2e3})},w=()=>{u(p=>(p+1)%(n.images.length||1))},x=()=>{u(p=>(p-1+(n.images.length||1))%(n.images.length||1))},C={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5,ease:"easeOut"}},exit:{opacity:0,y:-20,transition:{duration:.3,ease:"easeIn"}}},v={initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95}};return n?h.jsxs(L.div,{variants:C,initial:"initial",animate:"animate",exit:"exit",className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 animate-fade-in",children:[h.jsxs("div",{className:"grid md:grid-cols-2 gap-8 md:gap-12 items-start",children:[h.jsxs(L.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.5,delay:.1},className:"relative",children:[h.jsx(fP,{mode:"wait",children:h.jsx(L.div,{variants:v,initial:"initial",animate:"animate",exit:"exit",className:"aspect-square w-full rounded-xl shadow-2xl overflow-hidden bg-pastel-light",children:h.jsx("img",{class:"w-full h-full object-cover",alt:`${n.name} - view ${l+1}`,src:"https://images.unsplash.com/photo-1580928087639-6bfb993701a0?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=600&h=600&fit=crop&ixid=M3w2Mzg4NTR8MHwxfHJhbmRvbXx8fHx8fHx8fDE3MTcxNzE0Njd8"})},l)}),n.images&&n.images.length>1&&h.jsxs(h.Fragment,{children:[h.jsx(ce,{variant:"ghost",size:"icon",onClick:x,className:"absolute top-1/2 left-2 -translate-y-1/2 bg-white/70 hover:bg-pastel-medium text-pastel-accent rounded-full backdrop-blur-sm z-10",children:h.jsx(gP,{size:28})}),h.jsx(ce,{variant:"ghost",size:"icon",onClick:w,className:"absolute top-1/2 right-2 -translate-y-1/2 bg-white/70 hover:bg-pastel-medium text-pastel-accent rounded-full backdrop-blur-sm z-10",children:h.jsx(vP,{size:28})}),h.jsx("div",{className:"mt-4 flex space-x-2 justify-center",children:n.images.map((p,m)=>h.jsx("button",{onClick:()=>u(m),className:`w-16 h-16 rounded-md overflow-hidden border-2 ${m===l?"border-pastel-dark ring-2 ring-pastel-dark":"border-transparent hover:border-pastel-medium"} transition-all`,children:h.jsx("img",{class:"w-full h-full object-cover",alt:`Thumbnail ${m+1}`,src:"https://images.unsplash.com/photo-1516762689617-e1cffcef479d?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=100&h=100&fit=crop&ixid=M3w2Mzg4NTR8MHwxfHJhbmRvbXx8fHx8fHx8fDE3MTcxNzE1MDV8"})},m))})]})]}),h.jsxs(L.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.5,delay:.2},className:"space-y-6",children:[h.jsx("span",{className:"text-sm text-pastel-dark font-medium uppercase tracking-wider",children:n.category}),h.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-pastel-accent",children:n.name}),h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsxs("div",{className:"flex text-yellow-400",children:[[...Array(Math.floor(n.rating))].map((p,m)=>h.jsx(Ra,{size:20,className:"fill-current"},m)),n.rating%1!==0&&h.jsx(Ra,{size:20,className:"fill-current opacity-50"}),[...Array(5-Math.ceil(n.rating))].map((p,m)=>h.jsx(Ra,{size:20,className:"text-gray-300"},`empty-${m}`))]}),h.jsxs("span",{className:"text-sm text-pastel-accent/70",children:["(",n.reviews," reviews)"]})]}),h.jsxs("p",{className:"text-2xl font-semibold text-pastel-dark",children:["$",n.price]}),h.jsx("p",{className:"text-pastel-accent/80 leading-relaxed",children:n.description}),n.options&&Object.keys(n.options).map(p=>h.jsxs("div",{className:"space-y-2",children:[h.jsxs("h3",{className:"text-md font-semibold text-pastel-accent capitalize",children:[p,": ",h.jsx("span",{className:"font-normal text-pastel-accent/90",children:i[p]})]}),h.jsx("div",{className:"flex flex-wrap gap-2",children:n.options[p].map(m=>h.jsxs(ce,{variant:i[p]===m.name?"default":"outline",size:"sm",onClick:()=>d(p,m.name),className:`
                          ${i[p]===m.name?"bg-pastel-dark text-white border-pastel-dark":"border-pastel-medium text-pastel-accent hover:bg-pastel-light hover:border-pastel-dark"}
                          ${p==="colors"?"h-8 w-8 p-0 rounded-full":""}
                        `,style:p==="colors"?{backgroundColor:m.value}:{},"aria-label":p==="colors"?`Select color ${m.name}`:`Select ${p} ${m.name}`,children:[p!=="colors"&&m.name,p==="colors"&&i[p]===m.name&&h.jsx(Xf,{size:16,className:"text-white/70"})]},m.name))})]},p)),h.jsxs("div",{className:"flex items-center space-x-4",children:[h.jsx("h3",{className:"text-md font-semibold text-pastel-accent",children:"Quantity:"}),h.jsxs("div",{className:"flex items-center border border-pastel-medium rounded-md",children:[h.jsx(ce,{variant:"ghost",size:"sm",onClick:()=>c(-1),className:"px-3 text-pastel-accent hover:bg-pastel-light rounded-r-none",children:"-"}),h.jsx("span",{className:"px-4 text-pastel-accent",children:s}),h.jsx(ce,{variant:"ghost",size:"sm",onClick:()=>c(1),className:"px-3 text-pastel-accent hover:bg-pastel-light rounded-l-none",children:"+"})]}),h.jsxs("span",{className:"text-sm text-pastel-accent/70",children:[n.stock," in stock"]})]}),h.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 pt-4",children:[h.jsxs(ce,{size:"lg",onClick:f,className:"flex-1 bg-pastel-accent text-pastel-bg hover:bg-pastel-accent/90 shadow-lg",children:[h.jsx(rv,{size:20,className:"mr-2"})," Add to Cart"]}),h.jsxs(ce,{size:"lg",variant:"outline",onClick:g,className:"flex-1 border-pastel-dark text-pastel-accent hover:bg-pastel-dark hover:text-pastel-bg shadow-md",children:[h.jsx(ji,{size:20,className:"mr-2"})," Add to Wishlist"]})]}),h.jsxs("div",{className:"text-sm text-pastel-accent/70",children:["SKU: ",n.sku.replace(/COLOR|SIZE|SCENT/g,p=>{var m;return((m=i[p.toLowerCase()+"s"])==null?void 0:m.toUpperCase())||p})]})]})]}),h.jsxs(L.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},className:"mt-16 pt-8 border-t border-pastel-medium/50",children:[h.jsx("h2",{className:"text-2xl font-semibold text-pastel-accent mb-4",children:"Product Details"}),h.jsx("div",{className:"prose prose-lg max-w-none text-pastel-accent/80 leading-relaxed",children:h.jsx("p",{children:n.longDescription})})]}),h.jsxs(L.section,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},className:"mt-16 pt-8 border-t border-pastel-medium/50",children:[h.jsx("h2",{className:"text-2xl md:text-3xl font-bold text-pastel-accent text-center mb-12",children:"You Might Also Like"}),h.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:oE.filter(p=>p.id!==n.id).map((p,m)=>h.jsx(L.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:m*.1},children:h.jsx(yc,{product:p})},p.id))})]})]}):h.jsx("div",{className:"container mx-auto text-center py-20 text-xl text-pastel-accent",children:"Loading product details..."})};function In(e){const t=y.useRef(e);return y.useEffect(()=>{t.current=e}),y.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function aE(e,t=globalThis==null?void 0:globalThis.document){const n=In(e);y.useEffect(()=>{const r=i=>{i.key==="Escape"&&n(i)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var lE="DismissableLayer",Yl="dismissableLayer.update",uE="dismissableLayer.pointerDownOutside",cE="dismissableLayer.focusOutside",op,$v=y.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Wv=y.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:i,onFocusOutside:o,onInteractOutside:s,onDismiss:a,...l}=e,u=y.useContext($v),[d,c]=y.useState(null),f=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,g]=y.useState({}),w=je(t,P=>c(P)),x=Array.from(u.layers),[C]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),v=x.indexOf(C),p=d?x.indexOf(d):-1,m=u.layersWithOutsidePointerEventsDisabled.size>0,S=p>=v,k=fE(P=>{const E=P.target,R=[...u.branches].some(j=>j.contains(E));!S||R||(i==null||i(P),s==null||s(P),P.defaultPrevented||a==null||a())},f),b=pE(P=>{const E=P.target;[...u.branches].some(j=>j.contains(E))||(o==null||o(P),s==null||s(P),P.defaultPrevented||a==null||a())},f);return aE(P=>{p===u.layers.size-1&&(r==null||r(P),!P.defaultPrevented&&a&&(P.preventDefault(),a()))},f),y.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(op=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),sp(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(f.body.style.pointerEvents=op)}},[d,f,n,u]),y.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),sp())},[d,u]),y.useEffect(()=>{const P=()=>g({});return document.addEventListener(Yl,P),()=>document.removeEventListener(Yl,P)},[]),h.jsx(ye.div,{...l,ref:w,style:{pointerEvents:m?S?"auto":"none":void 0,...e.style},onFocusCapture:fe(e.onFocusCapture,b.onFocusCapture),onBlurCapture:fe(e.onBlurCapture,b.onBlurCapture),onPointerDownCapture:fe(e.onPointerDownCapture,k.onPointerDownCapture)})});Wv.displayName=lE;var dE="DismissableLayerBranch",Hv=y.forwardRef((e,t)=>{const n=y.useContext($v),r=y.useRef(null),i=je(t,r);return y.useEffect(()=>{const o=r.current;if(o)return n.branches.add(o),()=>{n.branches.delete(o)}},[n.branches]),h.jsx(ye.div,{...e,ref:i})});Hv.displayName=dE;function fE(e,t=globalThis==null?void 0:globalThis.document){const n=In(e),r=y.useRef(!1),i=y.useRef(()=>{});return y.useEffect(()=>{const o=a=>{if(a.target&&!r.current){let l=function(){Kv(uE,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",i.current),i.current=l,t.addEventListener("click",i.current,{once:!0})):l()}else t.removeEventListener("click",i.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",o)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",o),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function pE(e,t=globalThis==null?void 0:globalThis.document){const n=In(e),r=y.useRef(!1);return y.useEffect(()=>{const i=o=>{o.target&&!r.current&&Kv(cE,n,{originalEvent:o},{discrete:!1})};return t.addEventListener("focusin",i),()=>t.removeEventListener("focusin",i)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function sp(){const e=new CustomEvent(Yl);document.dispatchEvent(e)}function Kv(e,t,n,{discrete:r}){const i=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),r?hv(i,o):i.dispatchEvent(o)}var hE=Wv,mE=Hv,gE="Portal",Gv=y.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[i,o]=y.useState(!1);br(()=>o(!0),[]);const s=n||i&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?Hx.createPortal(h.jsx(ye.div,{...r,ref:t}),s):null});Gv.displayName=gE;var vE=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),yE="VisuallyHidden",Pc=y.forwardRef((e,t)=>h.jsx(ye.span,{...e,ref:t,style:{...vE,...e.style}}));Pc.displayName=yE;var bc="ToastProvider",[Ec,xE,wE]=Ev("Toast"),[Qv,YE]=Fs("Toast",[wE]),[SE,Us]=Qv(bc),Yv=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:i="right",swipeThreshold:o=50,children:s}=e,[a,l]=y.useState(null),[u,d]=y.useState(0),c=y.useRef(!1),f=y.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${bc}\`. Expected non-empty \`string\`.`),h.jsx(Ec.Provider,{scope:t,children:h.jsx(SE,{scope:t,label:n,duration:r,swipeDirection:i,swipeThreshold:o,toastCount:u,viewport:a,onViewportChange:l,onToastAdd:y.useCallback(()=>d(g=>g+1),[]),onToastRemove:y.useCallback(()=>d(g=>g-1),[]),isFocusedToastEscapeKeyDownRef:c,isClosePausedRef:f,children:s})})};Yv.displayName=bc;var Xv="ToastViewport",CE=["F8"],Xl="toast.viewportPause",ql="toast.viewportResume",qv=y.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=CE,label:i="Notifications ({hotkey})",...o}=e,s=Us(Xv,n),a=xE(n),l=y.useRef(null),u=y.useRef(null),d=y.useRef(null),c=y.useRef(null),f=je(t,c,s.onViewportChange),g=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=s.toastCount>0;y.useEffect(()=>{const C=v=>{var m;r.length!==0&&r.every(S=>v[S]||v.code===S)&&((m=c.current)==null||m.focus())};return document.addEventListener("keydown",C),()=>document.removeEventListener("keydown",C)},[r]),y.useEffect(()=>{const C=l.current,v=c.current;if(w&&C&&v){const p=()=>{if(!s.isClosePausedRef.current){const b=new CustomEvent(Xl);v.dispatchEvent(b),s.isClosePausedRef.current=!0}},m=()=>{if(s.isClosePausedRef.current){const b=new CustomEvent(ql);v.dispatchEvent(b),s.isClosePausedRef.current=!1}},S=b=>{!C.contains(b.relatedTarget)&&m()},k=()=>{C.contains(document.activeElement)||m()};return C.addEventListener("focusin",p),C.addEventListener("focusout",S),C.addEventListener("pointermove",p),C.addEventListener("pointerleave",k),window.addEventListener("blur",p),window.addEventListener("focus",m),()=>{C.removeEventListener("focusin",p),C.removeEventListener("focusout",S),C.removeEventListener("pointermove",p),C.removeEventListener("pointerleave",k),window.removeEventListener("blur",p),window.removeEventListener("focus",m)}}},[w,s.isClosePausedRef]);const x=y.useCallback(({tabbingDirection:C})=>{const p=a().map(m=>{const S=m.ref.current,k=[S,...DE(S)];return C==="forwards"?k:k.reverse()});return(C==="forwards"?p.reverse():p).flat()},[a]);return y.useEffect(()=>{const C=c.current;if(C){const v=p=>{var k,b,P;const m=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!m){const E=document.activeElement,R=p.shiftKey;if(p.target===C&&R){(k=u.current)==null||k.focus();return}const D=x({tabbingDirection:R?"backwards":"forwards"}),H=D.findIndex(A=>A===E);Aa(D.slice(H+1))?p.preventDefault():R?(b=u.current)==null||b.focus():(P=d.current)==null||P.focus()}};return C.addEventListener("keydown",v),()=>C.removeEventListener("keydown",v)}},[a,x]),h.jsxs(mE,{ref:l,role:"region","aria-label":i.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&h.jsx(Zl,{ref:u,onFocusFromOutsideViewport:()=>{const C=x({tabbingDirection:"forwards"});Aa(C)}}),h.jsx(Ec.Slot,{scope:n,children:h.jsx(ye.ol,{tabIndex:-1,...o,ref:f})}),w&&h.jsx(Zl,{ref:d,onFocusFromOutsideViewport:()=>{const C=x({tabbingDirection:"backwards"});Aa(C)}})]})});qv.displayName=Xv;var Zv="ToastFocusProxy",Zl=y.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...i}=e,o=Us(Zv,n);return h.jsx(Pc,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const a=s.relatedTarget;!((u=o.viewport)!=null&&u.contains(a))&&r()}})});Zl.displayName=Zv;var Bi="Toast",kE="toast.swipeStart",PE="toast.swipeMove",bE="toast.swipeCancel",EE="toast.swipeEnd",Jv=y.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:i,onOpenChange:o,...s}=e,[a,l]=xc({prop:r,defaultProp:i??!0,onChange:o,caller:Bi});return h.jsx(wc,{present:n||a,children:h.jsx(jE,{open:a,...s,ref:t,onClose:()=>l(!1),onPause:In(e.onPause),onResume:In(e.onResume),onSwipeStart:fe(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:fe(e.onSwipeMove,u=>{const{x:d,y:c}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${c}px`)}),onSwipeCancel:fe(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:fe(e.onSwipeEnd,u=>{const{x:d,y:c}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${c}px`),l(!1)})})})});Jv.displayName=Bi;var[TE,NE]=Qv(Bi,{onClose(){}}),jE=y.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:i,open:o,onClose:s,onEscapeKeyDown:a,onPause:l,onResume:u,onSwipeStart:d,onSwipeMove:c,onSwipeCancel:f,onSwipeEnd:g,...w}=e,x=Us(Bi,n),[C,v]=y.useState(null),p=je(t,A=>v(A)),m=y.useRef(null),S=y.useRef(null),k=i||x.duration,b=y.useRef(0),P=y.useRef(k),E=y.useRef(0),{onToastAdd:R,onToastRemove:j}=x,O=In(()=>{var G;(C==null?void 0:C.contains(document.activeElement))&&((G=x.viewport)==null||G.focus()),s()}),D=y.useCallback(A=>{!A||A===1/0||(window.clearTimeout(E.current),b.current=new Date().getTime(),E.current=window.setTimeout(O,A))},[O]);y.useEffect(()=>{const A=x.viewport;if(A){const G=()=>{D(P.current),u==null||u()},z=()=>{const Q=new Date().getTime()-b.current;P.current=P.current-Q,window.clearTimeout(E.current),l==null||l()};return A.addEventListener(Xl,z),A.addEventListener(ql,G),()=>{A.removeEventListener(Xl,z),A.removeEventListener(ql,G)}}},[x.viewport,k,l,u,D]),y.useEffect(()=>{o&&!x.isClosePausedRef.current&&D(k)},[o,k,x.isClosePausedRef,D]),y.useEffect(()=>(R(),()=>j()),[R,j]);const H=y.useMemo(()=>C?sy(C):null,[C]);return x.viewport?h.jsxs(h.Fragment,{children:[H&&h.jsx(RE,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:H}),h.jsx(TE,{scope:n,onClose:O,children:Ts.createPortal(h.jsx(Ec.ItemSlot,{scope:n,children:h.jsx(hE,{asChild:!0,onEscapeKeyDown:fe(a,()=>{x.isFocusedToastEscapeKeyDownRef.current||O(),x.isFocusedToastEscapeKeyDownRef.current=!1}),children:h.jsx(ye.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":o?"open":"closed","data-swipe-direction":x.swipeDirection,...w,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:fe(e.onKeyDown,A=>{A.key==="Escape"&&(a==null||a(A.nativeEvent),A.nativeEvent.defaultPrevented||(x.isFocusedToastEscapeKeyDownRef.current=!0,O()))}),onPointerDown:fe(e.onPointerDown,A=>{A.button===0&&(m.current={x:A.clientX,y:A.clientY})}),onPointerMove:fe(e.onPointerMove,A=>{if(!m.current)return;const G=A.clientX-m.current.x,z=A.clientY-m.current.y,Q=!!S.current,N=["left","right"].includes(x.swipeDirection),_=["left","up"].includes(x.swipeDirection)?Math.min:Math.max,F=N?_(0,G):0,V=N?0:_(0,z),$=A.pointerType==="touch"?10:2,Y={x:F,y:V},Ue={originalEvent:A,delta:Y};Q?(S.current=Y,po(PE,c,Ue,{discrete:!1})):ap(Y,x.swipeDirection,$)?(S.current=Y,po(kE,d,Ue,{discrete:!1}),A.target.setPointerCapture(A.pointerId)):(Math.abs(G)>$||Math.abs(z)>$)&&(m.current=null)}),onPointerUp:fe(e.onPointerUp,A=>{const G=S.current,z=A.target;if(z.hasPointerCapture(A.pointerId)&&z.releasePointerCapture(A.pointerId),S.current=null,m.current=null,G){const Q=A.currentTarget,N={originalEvent:A,delta:G};ap(G,x.swipeDirection,x.swipeThreshold)?po(EE,g,N,{discrete:!0}):po(bE,f,N,{discrete:!0}),Q.addEventListener("click",_=>_.preventDefault(),{once:!0})}})})})}),x.viewport)})]}):null}),RE=e=>{const{__scopeToast:t,children:n,...r}=e,i=Us(Bi,t),[o,s]=y.useState(!1),[a,l]=y.useState(!1);return _E(()=>s(!0)),y.useEffect(()=>{const u=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:h.jsx(Gv,{asChild:!0,children:h.jsx(Pc,{...r,children:o&&h.jsxs(h.Fragment,{children:[i.label," ",n]})})})},ME="ToastTitle",ey=y.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return h.jsx(ye.div,{...r,ref:t})});ey.displayName=ME;var AE="ToastDescription",ty=y.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return h.jsx(ye.div,{...r,ref:t})});ty.displayName=AE;var ny="ToastAction",ry=y.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?h.jsx(oy,{altText:n,asChild:!0,children:h.jsx(Tc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${ny}\`. Expected non-empty \`string\`.`),null)});ry.displayName=ny;var iy="ToastClose",Tc=y.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,i=NE(iy,n);return h.jsx(oy,{asChild:!0,children:h.jsx(ye.button,{type:"button",...r,ref:t,onClick:fe(e.onClick,i.onClose)})})});Tc.displayName=iy;var oy=y.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...i}=e;return h.jsx(ye.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...i,ref:t})});function sy(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),LE(r)){const i=r.ariaHidden||r.hidden||r.style.display==="none",o=r.dataset.radixToastAnnounceExclude==="";if(!i)if(o){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...sy(r))}}),t}function po(e,t,n,{discrete:r}){const i=n.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),r?hv(i,o):i.dispatchEvent(o)}var ap=(e,t,n=0)=>{const r=Math.abs(e.x),i=Math.abs(e.y),o=r>i;return t==="left"||t==="right"?o&&r>n:!o&&i>n};function _E(e=()=>{}){const t=In(e);br(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function LE(e){return e.nodeType===e.ELEMENT_NODE}function DE(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const i=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||i?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Aa(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var VE=Yv,ay=qv,ly=Jv,uy=ey,cy=ty,dy=ry,fy=Tc;const IE=VE,py=re.forwardRef(({className:e,...t},n)=>h.jsx(ay,{ref:n,className:Ye("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));py.displayName=ay.displayName;const OE=vc("data-[swipe=move]:transition-none group relative pointer-events-auto flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full data-[state=closed]:slide-out-to-right-full",{variants:{variant:{default:"bg-background border",destructive:"group destructive border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),hy=re.forwardRef(({className:e,variant:t,...n},r)=>h.jsx(ly,{ref:r,className:Ye(OE({variant:t}),e),...n}));hy.displayName=ly.displayName;const FE=re.forwardRef(({className:e,...t},n)=>h.jsx(dy,{ref:n,className:Ye("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-destructive/30 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));FE.displayName=dy.displayName;const my=re.forwardRef(({className:e,...t},n)=>h.jsx(fy,{ref:n,className:Ye("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:h.jsx(mc,{className:"h-4 w-4"})}));my.displayName=fy.displayName;const gy=re.forwardRef(({className:e,...t},n)=>h.jsx(uy,{ref:n,className:Ye("text-sm font-semibold",e),...t}));gy.displayName=uy.displayName;const vy=re.forwardRef(({className:e,...t},n)=>h.jsx(cy,{ref:n,className:Ye("text-sm opacity-90",e),...t}));vy.displayName=cy.displayName;function zE(){const{toasts:e}=zi();return h.jsxs(IE,{children:[e.map(({id:t,title:n,description:r,action:i,...o})=>h.jsxs(hy,{...o,children:[h.jsxs("div",{className:"grid gap-1",children:[n&&h.jsx(gy,{children:n}),r&&h.jsx(vy,{children:r})]}),i,h.jsx(my,{})]},t)),h.jsx(py,{})]})}function BE(){const{pathname:e,hash:t}=zn();return y.useEffect(()=>{if(t){const n=document.getElementById(t.substring(1));n?n.scrollIntoView({behavior:"smooth"}):window.scrollTo(0,0)}else window.scrollTo(0,0)},[e,t]),null}const UE={apiKey:"AIzaSyCEOZg9yIUomHqeoh5w6QRA0lyvFQev7_s",authDomain:"c-cube-eg.firebaseapp.com",projectId:"c-cube-eg",storageBucket:"c-cube-eg.firebasestorage.app",messagingSenderId:"386897939069",appId:"1:386897939069:web:80d4f3bdd82b69e395ff14",measurementId:"G-7C3WZ5X12M"},$E=initializeApp(UE);getAnalytics($E);function WE(){return h.jsxs(W1,{children:[h.jsx(BE,{}),h.jsxs("div",{className:"flex flex-col min-h-screen bg-pastel-bg",children:[h.jsx(fb,{}),h.jsx("main",{className:"flex-grow",children:h.jsxs(V1,{children:[h.jsx(Kn,{path:"/",element:h.jsx(yb,{})}),h.jsx(Kn,{path:"/shop",element:h.jsx(Jb,{})}),h.jsx(Kn,{path:"/shop/:productId",element:h.jsx(sE,{})}),h.jsx(Kn,{path:"/about",element:h.jsx(eE,{})}),h.jsx(Kn,{path:"/contact",element:h.jsx(tE,{})})]})}),h.jsx(pb,{}),h.jsx(zE,{})]})]})}_a.createRoot(document.getElementById("root")).render(h.jsx(re.StrictMode,{children:h.jsx(WE,{})}));
