import React from 'react';
    import { motion } from 'framer-motion';
    import { Heart, Users, Package } from 'lucide-react';

    const AboutPage = () => {
      const pageVariants = {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
        exit: { opacity: 0, y: -20, transition: { duration: 0.5, ease: "easeIn" } }
      };

      const sectionVariants = {
        hidden: { opacity: 0, y: 50 },
        visible: { opacity: 1, y: 0, transition: { duration: 0.7, ease: "easeOut" } }
      };

      return (
        <motion.div 
          initial="initial"
          animate="animate"
          exit="exit"
          variants={pageVariants}
          className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 animate-fade-in"
        >
          <motion.h1 
            initial={{ opacity:0, y: -30 }}
            animate={{ opacity:1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
            className="text-4xl md:text-5xl font-bold text-pastel-accent text-center mb-12"
          >
            Our Story: Handmade with Heart
          </motion.h1>

          <motion.section 
            variants={sectionVariants} 
            initial="hidden" 
            whileInView="visible" 
            viewport={{ once: true, amount: 0.3 }}
            className="mb-16 grid md:grid-cols-2 gap-12 items-center"
          >
            <div className="prose prose-lg max-w-none text-pastel-accent/90">
              <p className="lead text-xl">
                Welcome to C³ – C Cube, where creativity and craftsmanship intertwine. Our name, "C Cube,"
                represents our passion for diverse crafts, spanning six core categories: Crochet, Candles, Crafts, Clay, Concrete, and Canvas. 
                Each piece is a labor of love, meticulously handmade with a touch of pastel charm and a cozy, feminine aesthetic.
              </p>
              <p>
                Our journey began with a simple desire: to share the joy of handmade goods that bring warmth and personality to everyday life. 
                We believe in the beauty of imperfection and the unique story each handcrafted item tells. From delicate crochet scrunchies to 
                minimalist concrete decor, every product in our shop is designed to evoke a sense of comfort and delight.
              </p>
              <p>
                At C³ – C Cube, we pour our hearts into every creation, ensuring quality and artistry. We hope our items find a special place
                in your home and heart, adding a sprinkle of handmade magic to your world.
              </p>
            </div>
            <motion.div 
              className="rounded-xl overflow-hidden shadow-2xl"
              initial={{ scale: 0.8, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true, amount: 0.3 }}
            >
              <img  class="w-full h-auto object-cover" alt="Founder working on crafts" src="https://images.unsplash.com/photo-1607063696672-9dbc90ef3ebf" />
            </motion.div>
          </motion.section>

          <motion.section 
            variants={sectionVariants} 
            initial="hidden" 
            whileInView="visible" 
            viewport={{ once: true, amount: 0.3 }}
            className="py-16 bg-pastel-light rounded-xl shadow-lg"
          >
            <h2 className="text-3xl font-bold text-pastel-accent text-center mb-12">Our Values</h2>
            <div className="grid md:grid-cols-3 gap-8 text-center px-8">
              <motion.div 
                className="p-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <Heart size={48} className="mx-auto mb-4 text-pastel-dark" />
                <h3 className="text-2xl font-semibold text-pastel-accent mb-2">Passion-Driven</h3>
                <p className="text-pastel-accent/80">
                  Every item is created with genuine love and enthusiasm for the craft. We believe this passion shines through in the quality and uniqueness of our products.
                </p>
              </motion.div>
              <motion.div 
                className="p-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <Package size={48} className="mx-auto mb-4 text-pastel-dark" />
                <h3 className="text-2xl font-semibold text-pastel-accent mb-2">Quality Craftsmanship</h3>
                <p className="text-pastel-accent/80">
                  We use high-quality materials and pay meticulous attention to detail, ensuring each piece is beautiful, durable, and well-made.
                </p>
              </motion.div>
              <motion.div 
                className="p-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <Users size={48} className="mx-auto mb-4 text-pastel-dark" />
                <h3 className="text-2xl font-semibold text-pastel-accent mb-2">Customer Joy</h3>
                <p className="text-pastel-accent/80">
                  Our greatest reward is bringing a smile to your face. We strive to create pieces that you'll cherish and that add a touch of happiness to your life.
                </p>
              </motion.div>
            </div>
          </motion.section>

          <motion.section 
            variants={sectionVariants} 
            initial="hidden" 
            whileInView="visible" 
            viewport={{ once: true, amount: 0.3 }}
            className="mt-16 text-center"
          >
            <h2 className="text-3xl font-bold text-pastel-accent mb-6">Meet the Maker</h2>
            <div className="max-w-3xl mx-auto">
              <motion.div 
                className="w-40 h-40 mx-auto mb-6 rounded-full overflow-hidden shadow-xl border-4 border-pastel-medium"
                initial={{ scale: 0.5, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, type: "spring", stiffness: 120 }}
                viewport={{ once: true }}
              >
                <img  class="w-full h-full object-cover" alt="Portrait of the shop owner" src="https://images.unsplash.com/photo-1633887091219-265c29eac4cd" />
              </motion.div>
              <p className="text-xl text-pastel-accent/90 mb-4">
                Hi, I'm [Your Name/Fictional Name], the hands and heart behind C³ – C Cube! Crafting has always been my sanctuary, a way to express creativity and find peace. 
                I started this little shop to share my passion with you and to spread a little bit of handmade joy. Thank you for supporting my dream!
              </p>
            </div>
          </motion.section>
        </motion.div>
      );
    };

    export default AboutPage;